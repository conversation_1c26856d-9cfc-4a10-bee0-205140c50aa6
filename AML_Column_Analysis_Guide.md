# AML Column Analysis Guide

## Problem: Missing Columns in AML Data Analysis

When the AML Data Analyzer shows "11 out of 17 expected columns found", it means 6 required columns are missing from the uploaded file.

## Complete List of 17 Required AML Columns

1. **CIF** - Customer Identification File number
2. **ENTERPRISE_NAME** - Company/business name
3. **BUSINESS_REGISTRATION_NUMBER** - Official registration number
4. **COUNTRY_OF_REGISTRATION** - Country where business is registered
5. **DATE_FORM** - Date of form submission/registration
6. **STATE_OF_REGISTRATION** - State/province of registration
7. **HOME_ADDRESS** - Home address of business
8. **MAILING_ADDRESS** - Mailing address
9. **ADD01** - Additional address field 1
10. **DEFAULT** - Default indicator field
11. **OFFICE_ADDRESS** - Office address
12. **EMAIL** - Email address
13. **CONTACT_NO** - Contact number/phone
14. **ANNUALTURNOVER** - Annual turnover amount
15. **INDUSTRY_TYPE** - Type of industry/business
16. **CUSTOMER_SEGMENT** - Customer segment classification
17. **UDF_CUSTOMERSEGMENT** - User-defined customer segment

## Diagnostic Steps

### Step 1: Use the Column Diagnostic Tool
1. Open `column_diagnostic.html` in your browser
2. Upload your CSV file
3. Review the detailed analysis showing:
   - Which columns are found vs missing
   - Exact column names in your file
   - Suggestions for similar column names

### Step 2: Check Browser Console
1. Open the main AML analyzer (`index.html`)
2. Open browser developer tools (F12)
3. Go to Console tab
4. Upload your file and check for debug messages showing:
   - File columns found
   - Column matching results
   - Missing columns list

### Step 3: Common Issues and Solutions

#### Issue 1: Column Name Variations
**Problem**: Your file has similar but not exact column names
**Examples**:
- `Customer_Segment` instead of `CUSTOMER_SEGMENT`
- `UDF_CustomerSegment` instead of `UDF_CUSTOMERSEGMENT`
- `Business_Reg_Number` instead of `BUSINESS_REGISTRATION_NUMBER`

**Solution**: Rename columns in your file to match exact names (case-insensitive matching is supported)

#### Issue 2: Extra Spaces or Special Characters
**Problem**: Column names have extra spaces or special characters
**Examples**:
- `" CIF "` (spaces around name)
- `CIF\t` (tab character)
- `CIF*` (special character)

**Solution**: Clean column names to remove extra characters

#### Issue 3: Missing Columns Entirely
**Problem**: Your data source doesn't include all required fields
**Common missing columns**:
- `DATE_FORM`
- `STATE_OF_REGISTRATION`
- `ADD01`
- `DEFAULT`
- `UDF_CUSTOMERSEGMENT`

**Solution**: 
1. Add missing columns to your data export
2. If data is not available, add columns with placeholder values like "-" or empty strings
3. Ensure all 17 columns are present even if some contain no data

#### Issue 4: Column Order
**Problem**: Columns are in different order than expected
**Solution**: Column order doesn't matter - the analyzer matches by name, not position

## Test Files for Validation

### Complete File (All 17 columns):
- `sample_aml_data.csv` - Has all required columns
- `test_dash_blanks.csv` - Has all required columns with dash values

### Incomplete File (Missing 6 columns):
- `test_missing_columns.csv` - Missing: DATE_FORM, STATE_OF_REGISTRATION, ADD01, DEFAULT, OFFICE_ADDRESS, UDF_CUSTOMERSEGMENT

## How to Fix Missing Columns

### Option 1: Update Data Source
1. Modify your data export query/process to include all 17 required columns
2. Ensure column names match the required list exactly

### Option 2: Add Missing Columns Manually
1. Open your CSV file in Excel or text editor
2. Add missing column headers
3. Fill with appropriate data or placeholder values ("-" for blanks)

### Option 3: Use Data Transformation
1. Create a mapping/transformation script
2. Rename existing columns to match required names
3. Add missing columns with default values

## Column Matching Logic

The analyzer uses **case-insensitive** matching with **trimmed whitespace**:

```javascript
// This will match:
"CIF" === "cif" === " CIF " === "Cif"

// This will NOT match:
"CIF" !== "CIF_NUMBER"
"EMAIL" !== "EMAIL_ADDRESS"
```

## Validation Checklist

Before uploading your file, ensure:

- [ ] File contains exactly 17 columns
- [ ] Column names match the required list (case doesn't matter)
- [ ] No extra spaces or special characters in column names
- [ ] All required data fields are present (can be blank/empty)
- [ ] File is in CSV format or Excel format (.xlsx, .xls)

## Getting Help

If you're still having issues:

1. Use the diagnostic tool to identify specific missing columns
2. Check the browser console for detailed matching information
3. Compare your column names with the required list
4. Verify your data export includes all necessary fields

## Expected Results

When all columns are correctly matched:
- Summary should show "17 out of 17 expected columns found"
- No missing columns should be reported
- All analysis results will be available
- Address group analysis will work properly
