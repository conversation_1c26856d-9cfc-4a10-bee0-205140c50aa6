# AML Data Analyzer - Blank Value Counter

A web-based tool designed for AML (Anti-Money Laundering) teams to analyze uploaded Excel or CSV files and identify blank/missing values in required columns.

## Features

- **File Upload Support**: Accepts Excel (.xlsx, .xls) and CSV files
- **Drag & Drop Interface**: Easy file upload with drag-and-drop functionality
- **Comprehensive Analysis**: Analyzes all 17 required AML columns
- **Address Group Analysis**: Special grouping logic for address-related columns
- **Row Completeness Analysis**: Counts complete vs incomplete rows across all 17 required columns
- **Detailed Results**: Shows blank count, total rows, and percentage for each column
- **Missing Column Detection**: Identifies which required columns are missing from the uploaded file
- **Professional UI**: Clean, responsive design with Tailwind CSS

## Required AML Columns

The tool analyzes the following columns:

1. CIF
2. ENTERPRISE_NAME
3. BUSINESS_REGISTRATION_NUMBER
4. COUNTRY_OF_REGISTRATION
5. DATE_FORM
6. STATE_OF_REGISTRATION
7. HOME_ADDRESS
8. MAILING_ADDRESS
9. ADD01
10. DEFAULT
11. OFFICE_ADDRESS
12. EMAIL
13. CONTACT_NO
14. ANNUALTURNOVER
15. INDUSTRY_TYPE
16. CUSTOMER_SEGMENT
17. UDF_CUSTOMERSEGMENT

## How to Use

1. **Open the Application**: Open `index.html` in any modern web browser
2. **Upload File**: 
   - Drag and drop your Excel or CSV file onto the upload area, OR
   - Click the upload area to browse and select your file
3. **View Results**: The analysis results will display in a comprehensive table showing:
   - Column name
   - Status (Found/Missing)
   - Number of blank values
   - Total rows
   - Percentage of blank values

## Sample Data

A sample CSV file (`sample_aml_data.csv`) is included for testing purposes. This file contains:
- 20 sample records
- Intentionally missing values in various columns
- All required AML columns

## Analysis Details

### What Counts as "Blank"?
- Empty strings ("")
- Strings with only whitespace
- Dash character ("-")
- null values
- undefined values
- NaN (for numeric fields)

**Special attention to UDF_CUSTOMERSEGMENT**: This column is particularly important for AML analysis. The tool carefully detects all blank values in this field, including trailing empty values and dash characters in CSV files.

### Address Group Analysis
The tool includes special logic for address-related columns:
- **Address Columns**: HOME_ADDRESS, MAILING_ADDRESS, ADD01, OFFICE_ADDRESS
- **Grouping Rule**: A record counts as "1 blank" for the address group ONLY if ALL four address columns are completely empty
- **Individual Analysis**: Each address column is also analyzed individually
- **Display**: Address group appears as a separate row with blue highlighting

### Row Completeness Analysis
The tool provides comprehensive row-level analysis with five different AML-specific options:
- **Five Analysis Options**: Different exclusion criteria for varying AML requirements
- **Complete Rows**: Rows meeting all AML completeness criteria for each option
- **Incomplete Rows**: Rows missing required data for AML analysis
- **Visual Dashboard**: Color-coded cards showing counts and percentages for all options
- **Flexible Criteria**: Choose the option that best fits your data availability and requirements

#### Five Completeness Options
**Option 1 - Core Business Data Focus:**
- Excludes: DEFAULT, ADD01, CIF, DATE_FORM
- Required: 13 columns + at least one address field

**Option 2 - Essential Data Only:**
- Excludes: DEFAULT, ADD01, CIF, DATE_FORM, EMAIL
- Required: 12 columns + at least one address field

**Option 3 - Minimal Core Data:**
- Excludes: CIF, DATE_FORM, ADD01, DEFAULT, EMAIL, CONTACT_NO
- Required: 11 columns + at least one address field

**Option 4 - Basic Business Information:**
- Excludes: CIF, DATE_FORM, ADD01, DEFAULT, EMAIL, CONTACT_NO, UDF_CUSTOMERSEGMENT
- Required: 10 columns + at least one address field

**Option 5 - Contact-Enabled Business Data:**
- Excludes: CIF, DATE_FORM, ADD01, DEFAULT, EMAIL, UDF_CUSTOMERSEGMENT
- Required: 11 columns + at least one address field (includes CONTACT_NO)
- Features: Dedicated "Analysis Results 2" section with detailed insights

#### Completeness Criteria (All Options)
**Complete Row Requirements:**
- All required columns (after exclusions) must contain actual data
- At least ONE of: HOME_ADDRESS, MAILING_ADDRESS, or OFFICE_ADDRESS must have data
- No blanks, empty strings, dashes "-", null values, or whitespace-only values

**Incomplete Row:** Missing data in any required column or all three address fields are blank

### Analysis Results Sections

#### Main Analysis Results
The primary results table provides:
- **Green badges**: Individual columns found in the uploaded file
- **Blue badges**: Address group analysis (when applicable)
- **Red badges**: Required columns missing from the file
- **Red highlighting**: Rows for missing columns
- **Summary section**: Overview of analysis results including address group statistics

#### Analysis Results 2 - Option 5 Focus
A dedicated detailed section for Option 5 featuring:
- **Key Metrics Dashboard**: Visual cards showing completion statistics
- **Contact Capability Analysis**: Phone contact enablement insights
- **Incomplete Reasons Breakdown**: Detailed categorization of data gaps
- **Row-by-Row Analysis**: Individual record status and issues
- **Use Case Guidance**: Specific applications for contact-enabled data
- **Comparison Insights**: Trade-offs vs other options

## Technical Details

- **Frontend**: React 18 with Babel for JSX transformation
- **Styling**: Tailwind CSS for responsive design
- **File Parsing**: 
  - CSV: Papa Parse library
  - Excel: SheetJS (xlsx) library
- **No Backend Required**: Runs entirely in the browser

## Browser Compatibility

- Chrome 60+
- Firefox 60+
- Safari 12+
- Edge 79+

## Security

- All file processing happens locally in the browser
- No data is sent to external servers
- Files are processed in memory only

## Example Output

```
Column Name                    | Status  | Blank Count | Total Rows | Blank Percentage
CIF                           | Found   | 3           | 20         | 15.00%
ENTERPRISE_NAME               | Found   | 4           | 20         | 20.00%
BUSINESS_REGISTRATION_NUMBER  | Found   | 2           | 20         | 10.00%
...
```

This tool helps AML teams quickly identify data quality issues and ensure compliance with regulatory requirements.
