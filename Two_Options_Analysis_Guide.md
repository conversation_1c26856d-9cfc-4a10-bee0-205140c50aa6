# AML Row Completeness Analysis - Two Options

## Overview
The AML Data Analyzer now provides two different completeness analysis options, each excluding different sets of columns based on varying AML requirements and data availability scenarios.

## Option 1: Core Business Data Focus
**Excludes:** DEFAULT, ADD01, CIF, DATE_FORM

### Rationale
- **CIF**: Customer identifier may be system-generated and not essential for analysis
- **DATE_FORM**: Form submission dates may not be critical for risk assessment
- **DEFAULT**: Technical field not required for AML analysis
- **ADD01**: Additional address field often optional

### Required Columns (13 total)
1. ENTERPRISE_NAME
2. BUSINESS_REGISTRATION_NUMBER
3. COUNTRY_OF_REGISTRATION
4. STATE_OF_REGISTRATION
5. HOME_ADDRESS (OR)
6. MAILING_ADDRESS (OR)
7. OFFICE_ADDRESS (OR)
8. EMAIL
9. CONTACT_NO
10. ANNUALTURNOVER
11. INDUSTRY_TYPE
12. CUSTOMER_SEGMENT
13. UDF_CUSTOMERSEGMENT

### Address Logic
At least ONE of: HOME_ADDRESS, MAILING_ADDRESS, or OFFICE_ADDRESS must contain data

## Option 2: Essential Data Only
**Excludes:** DEFAULT, ADD01, CIF, DATE_FORM, EMAIL

### Rationale
- **All Option 1 exclusions** plus:
- **EMAIL**: Email addresses may not be available for all entities, especially in certain jurisdictions or for older records

### Required Columns (12 total)
1. ENTERPRISE_NAME
2. BUSINESS_REGISTRATION_NUMBER
3. COUNTRY_OF_REGISTRATION
4. STATE_OF_REGISTRATION
5. HOME_ADDRESS (OR)
6. MAILING_ADDRESS (OR)
7. OFFICE_ADDRESS (OR)
8. CONTACT_NO
9. ANNUALTURNOVER
10. INDUSTRY_TYPE
11. CUSTOMER_SEGMENT
12. UDF_CUSTOMERSEGMENT

### Address Logic
At least ONE of: HOME_ADDRESS, MAILING_ADDRESS, or OFFICE_ADDRESS must contain data

## Completeness Criteria (Both Options)

### Complete Row Requirements
✅ All required columns (after exclusions) contain actual data  
✅ At least ONE address field has data  
✅ No blank values (empty strings, dashes "-", null, whitespace-only)  

### Incomplete Row Conditions
❌ Missing data in any required column  
❌ ALL three address fields are blank  

## Expected Results Comparison

### Test File: test_two_options.csv (10 rows)

**Option 1 Results:**
- Complete rows: 5 (50%)
- Incomplete rows: 5 (50%)
- **Incomplete due to:** Missing EMAIL or UDF_CUSTOMERSEGMENT issues

**Option 2 Results:**
- Complete rows: 7 (70%)
- Incomplete rows: 3 (30%)
- **Incomplete due to:** Missing address data or UDF_CUSTOMERSEGMENT issues

### Analysis Impact
- **Option 2 shows higher completion rates** due to EMAIL exclusion
- **Option 1 is more comprehensive** for full contact information
- **Both options maintain address flexibility** and core business data requirements

## Use Cases

### Option 1: Comprehensive AML Analysis
**Best for:**
- Full KYC/AML compliance programs
- Jurisdictions requiring complete contact information
- Enhanced due diligence processes
- Customer onboarding with full data collection

### Option 2: Essential Risk Assessment
**Best for:**
- Basic risk screening
- Legacy data analysis where email may be missing
- Jurisdictions with limited data availability
- Quick compliance checks on core business information

## Implementation Details

### Display Format
Both options are shown simultaneously with:
- Color-coded cards (blue for Option 1, purple for Option 2)
- Side-by-side comparison
- Clear exclusion lists
- Percentage calculations

### Console Logging
```
Row Completeness Analysis - Two Options:
Option 1 - Excludes: DEFAULT, ADD01, CIF, DATE_FORM
  Complete rows: X (XX%)
  Incomplete rows: X (XX%)
Option 2 - Excludes: DEFAULT, ADD01, CIF, DATE_FORM, EMAIL
  Complete rows: X (XX%)
  Incomplete rows: X (XX%)
```

## Choosing the Right Option

### Consider Option 1 when:
- Email contact is required for your AML program
- You have comprehensive data collection processes
- Regulatory requirements include full contact information
- You need maximum data completeness for enhanced due diligence

### Consider Option 2 when:
- Working with legacy or incomplete datasets
- Email availability is inconsistent
- Focus is on core business identification and risk factors
- Need to maximize usable records for analysis

## Data Quality Insights

### Completion Rate Differences
The difference between Option 1 and Option 2 completion rates indicates:
- **Email data availability** in your dataset
- **Data collection completeness** over time
- **Potential areas for data improvement**

### Actionable Recommendations
- If Option 1 completion is significantly lower than Option 2, focus on improving email data collection
- Use Option 2 for immediate analysis while working to improve data completeness
- Monitor completion rate trends over time to assess data quality improvements

## Technical Notes

### Address Group Logic
Both options use the same address logic:
- Any one of the three address fields satisfies the address requirement
- Maintains consistency with existing address group analysis
- Provides flexibility for different business address scenarios

### Blank Value Detection
Both options use identical blank detection:
- Empty strings ("")
- Whitespace-only strings (" ")
- Dash characters ("-")
- Null or undefined values
- NaN values (for numeric fields)

This dual-option approach provides flexibility for different AML analysis requirements while maintaining comprehensive data quality assessment capabilities.
