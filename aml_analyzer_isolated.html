<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AML Data Analyzer - Isolated Version</title>
    <style>
        /* Completely self-contained CSS */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background-color: #f9fafb;
            min-height: 100vh;
            padding: 2rem;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .header {
            text-align: center;
            margin-bottom: 2rem;
        }
        
        .title {
            font-size: 2rem;
            font-weight: bold;
            color: #1f2937;
            margin-bottom: 0.5rem;
        }
        
        .subtitle {
            color: #6b7280;
            margin-bottom: 1rem;
        }
        
        .column-selector {
            display: inline-flex;
            align-items: center;
            gap: 1rem;
            padding: 0.75rem;
            background: white;
            border-radius: 0.5rem;
            border: 1px solid #d1d5db;
            box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
            margin-bottom: 1rem;
        }
        
        .column-selector label {
            font-size: 0.875rem;
            font-weight: 500;
            color: #374151;
        }
        
        .button-group {
            display: flex;
            gap: 0.5rem;
        }
        
        .btn {
            padding: 0.25rem 0.75rem;
            font-size: 0.75rem;
            font-weight: 500;
            border-radius: 0.375rem;
            border: none;
            cursor: pointer;
            transition: all 0.2s;
        }
        
        .btn-primary {
            background-color: #2563eb;
            color: white;
        }
        
        .btn-secondary {
            background-color: #f3f4f6;
            color: #374151;
        }
        
        .btn-secondary:hover {
            background-color: #e5e7eb;
        }
        
        .description {
            font-size: 0.75rem;
            color: #6b7280;
            margin-top: 0.75rem;
        }
        
        .debug-info {
            font-size: 0.75rem;
            font-family: monospace;
            color: #2563eb;
            margin-top: 0.5rem;
        }
        
        .upload-area {
            background: white;
            border-radius: 0.5rem;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            padding: 1.5rem;
            margin-bottom: 2rem;
        }
        
        .drop-zone {
            border: 2px dashed #cbd5e0;
            border-radius: 0.5rem;
            padding: 2rem;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s;
        }
        
        .drop-zone:hover, .drop-zone.drag-over {
            border-color: #3b82f6;
            background-color: #eff6ff;
        }
        
        .upload-icon {
            width: 3rem;
            height: 3rem;
            margin: 0 auto 1rem;
            color: #9ca3af;
        }
        
        .upload-text {
            font-size: 1.125rem;
            color: #4b5563;
            margin-bottom: 0.5rem;
        }
        
        .upload-subtext {
            font-size: 0.875rem;
            color: #6b7280;
        }
        
        .hidden {
            display: none;
        }
        
        .loading {
            text-align: center;
            margin-top: 1rem;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.75rem;
        }
        
        .spinner {
            width: 1.25rem;
            height: 1.25rem;
            border: 2px solid #e5e7eb;
            border-top: 2px solid #3b82f6;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }
        
        @keyframes spin {
            to { transform: rotate(360deg); }
        }
        
        .error {
            margin-top: 1rem;
            padding: 1rem;
            background-color: #fef2f2;
            border: 1px solid #fecaca;
            border-radius: 0.375rem;
            display: flex;
            gap: 0.75rem;
        }
        
        .error-icon {
            width: 1.25rem;
            height: 1.25rem;
            color: #ef4444;
            flex-shrink: 0;
        }
        
        .error-content h3 {
            font-size: 0.875rem;
            font-weight: 500;
            color: #991b1b;
        }
        
        .error-content p {
            font-size: 0.875rem;
            color: #b91c1c;
            margin-top: 0.25rem;
        }
        
        .results {
            background: white;
            border-radius: 0.5rem;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            padding: 1.5rem;
        }
        
        .results-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1.5rem;
        }
        
        .results-title {
            font-size: 1.5rem;
            font-weight: bold;
            color: #1f2937;
        }
        
        .file-info {
            font-size: 0.875rem;
            color: #4b5563;
        }
        
        .table-container {
            overflow-x: auto;
        }
        
        .results-table {
            width: 100%;
            background: white;
            border: 1px solid #e5e7eb;
            border-radius: 0.5rem;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        
        .results-table th {
            padding: 0.75rem 1.5rem;
            text-align: left;
            font-size: 0.75rem;
            font-weight: 500;
            color: #6b7280;
            text-transform: uppercase;
            letter-spacing: 0.05em;
            background-color: #f9fafb;
            border-bottom: 1px solid #e5e7eb;
        }
        
        .results-table td {
            padding: 1rem 1.5rem;
            font-size: 0.875rem;
            color: #1f2937;
            border-bottom: 1px solid #e5e7eb;
        }
        
        .results-table tr:hover {
            background-color: #f9fafb;
        }
        
        .row-address-group {
            background-color: #dbeafe !important;
            border-left: 4px solid #3b82f6;
        }
        
        .row-address-group:hover {
            background-color: #bfdbfe !important;
        }
        
        .row-missing {
            background-color: #fef2f2;
        }
        
        .status-badge {
            display: inline-flex;
            align-items: center;
            padding: 0.125rem 0.625rem;
            border-radius: 9999px;
            font-size: 0.75rem;
            font-weight: 500;
        }
        
        .status-found {
            background-color: #dcfce7;
            color: #166534;
        }
        
        .status-group {
            background-color: #dbeafe;
            color: #1e40af;
        }
        
        .status-missing {
            background-color: #fecaca;
            color: #991b1b;
        }
        
        .column-description {
            font-size: 0.75rem;
            color: #6b7280;
            margin-top: 0.25rem;
        }
        
        .special-rule {
            font-size: 0.75rem;
            color: #2563eb;
            margin-top: 0.25rem;
        }
        
        .summary {
            margin-top: 1.5rem;
            padding: 1rem;
            background-color: #eff6ff;
            border: 1px solid #bfdbfe;
            border-radius: 0.375rem;
        }
        
        .summary h3 {
            font-size: 0.875rem;
            font-weight: 500;
            color: #1e40af;
            margin-bottom: 0.5rem;
        }
        
        .summary-content {
            font-size: 0.875rem;
            color: #1e3a8a;
        }
        
        .summary-content p {
            margin-bottom: 0.25rem;
        }
        
        .special-note {
            margin-top: 0.5rem;
            color: #2563eb;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1 class="title">AML Data Analyzer</h1>
            <p class="subtitle">Upload Excel or CSV files to analyze blank values in required columns</p>
            
            <div class="column-selector">
                <label>Column Set:</label>
                <div class="button-group">
                    <button id="primaryBtn" class="btn btn-primary">Primary (AML Business)</button>
                    <button id="secondaryBtn" class="btn btn-secondary">Secondary (Individual)</button>
                </div>
            </div>
            
            <div class="description">
                <span id="columnDescription">Business entities: ENTERPRISE_NAME, BUSINESS_REGISTRATION_NUMBER, etc. (17 columns)</span>
            </div>
            
            <div class="debug-info">
                Current column set: <span id="currentColumnSet">primary</span> | Expected columns: <span id="expectedCount">17</span>
            </div>
        </div>

        <div class="upload-area">
            <div class="drop-zone" id="dropZone">
                <div class="text-center">
                    <svg class="upload-icon" stroke="currentColor" fill="none" viewBox="0 0 48 48">
                        <path d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
                    </svg>
                    <p class="upload-text">Drop your Excel or CSV file here, or click to browse</p>
                    <p class="upload-subtext">Supports .xlsx, .xls, and .csv files</p>
                </div>
                <input id="fileInput" type="file" accept=".csv,.xlsx,.xls" class="hidden">
            </div>

            <div id="loading" class="loading hidden">
                <div class="spinner"></div>
                <span>Analyzing file...</span>
            </div>

            <div id="error" class="error hidden">
                <svg class="error-icon" viewBox="0 0 20 20" fill="currentColor">
                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
                </svg>
                <div class="error-content">
                    <h3>Error</h3>
                    <p id="errorMessage"></p>
                </div>
            </div>
        </div>

        <div id="results" class="results hidden">
            <div class="results-header">
                <h2 class="results-title">Analysis Results</h2>
                <div class="file-info">
                    File: <span id="fileName"></span>
                </div>
            </div>

            <div class="table-container">
                <table class="results-table">
                    <thead>
                        <tr>
                            <th>Column Name</th>
                            <th>Status</th>
                            <th>Blank Count</th>
                            <th>Total Rows</th>
                            <th>Blank Percentage</th>
                        </tr>
                    </thead>
                    <tbody id="resultsTableBody">
                    </tbody>
                </table>
            </div>

            <div class="summary">
                <h3>Column Analysis Summary</h3>
                <div class="summary-content" id="summaryContent">
                </div>

                <!-- Debug Export Button -->
                <div style="margin-top: 1rem; padding-top: 1rem; border-top: 1px solid #e5e7eb;">
                    <button id="exportBlankAnalysis" class="btn btn-secondary" style="display: none;">
                        📊 Export CONTACT_NO Blank Analysis to CSV
                    </button>
                    <p style="font-size: 0.75rem; color: #6b7280; margin-top: 0.5rem;">
                        Use this to compare with Excel and identify the 4-row discrepancy
                    </p>
                </div>
            </div>
        </div>
    </div>

    <!-- Load external libraries -->
    <script src="https://unpkg.com/xlsx@0.18.5/dist/xlsx.full.min.js"></script>
    <script src="https://unpkg.com/papaparse@5.4.1/papaparse.min.js"></script>

    <script>
        // Wrap everything in an IIFE to avoid global conflicts
        (function() {
            'use strict';
            
            // Define column sets
            const EXPECTED_COLUMNS_PRIMARY = [
                'CIF', 'ENTERPRISE_NAME', 'BUSINESS_REGISTRATION_NUMBER', 'COUNTRY_OF_REGISTRATION',
                'DATE_FORM', 'STATE_OF_REGISTRATION', 'HOME_ADDRESS', 'MAILING_ADDRESS', 'ADD01',
                'DEFAULT', 'OFFICE_ADDRESS', 'EMAIL', 'CONTACT_NO', 'ANNUALTURNOVER',
                'INDUSTRY_TYPE', 'CUSTOMER_SEGMENT', 'UDF_CUSTOMERSEGMENT'
            ];

            const EXPECTED_COLUMNS_SECONDARY = [
                'CIF', 'CONTACT_NO', 'FULL_NAME', 'NATIONAL_IDENTIFIER', 'NO_OF_PTO', 'OCCUPATION',
                'INDUSTRY_SECTOR', 'CUSTOMER_SEGMENT', 'CUSTOMER_SEGMENT_UDF', 'INCOME_LEVEL',
                'HOME_ADDRESS', 'MAILING_ADDRESS', 'ADD01', 'DEFAULT', 'OFFICE_ADDRESS',
                'EMPLOYMENTSTATUS', 'DATE_OF_BIRTH', 'NATIONALITY', 'FATHERNAME', 'NO_OF_SIGNATURE'
            ];

            // State
            let currentColumnSet = 'primary';
            let originalData = null;
            let currentFile = null;
            let contactNoBlankRows = []; // Store detailed info about CONTACT_NO blank rows

            // DOM elements
            const primaryBtn = document.getElementById('primaryBtn');
            const secondaryBtn = document.getElementById('secondaryBtn');
            const columnDescription = document.getElementById('columnDescription');
            const currentColumnSetSpan = document.getElementById('currentColumnSet');
            const expectedCountSpan = document.getElementById('expectedCount');
            const dropZone = document.getElementById('dropZone');
            const fileInput = document.getElementById('fileInput');
            const loading = document.getElementById('loading');
            const error = document.getElementById('error');
            const errorMessage = document.getElementById('errorMessage');
            const results = document.getElementById('results');
            const fileName = document.getElementById('fileName');
            const resultsTableBody = document.getElementById('resultsTableBody');
            const summaryContent = document.getElementById('summaryContent');
            const exportBlankAnalysisBtn = document.getElementById('exportBlankAnalysis');

            // Utility functions
            function isBlank(value, columnName = '', rowIndex = -1) {
                // ULTRA-STRICT blank detection for ALL columns - only truly empty/null data
                const isValueBlank = (() => {
                    // Only count as blank: null, undefined, or completely empty strings
                    if (value === null || value === undefined) return true;
                    if (typeof value === 'string' && value === '') return true; // Only empty strings, nothing else
                    if (typeof value === 'number' && isNaN(value)) return true;
                    return false; // Everything else is NOT blank (including 0, spaces, dashes, any text)
                })();

                // Debug logging for any column that's blank (focusing on CONTACT_NO for debugging)
                if (columnName === 'CONTACT_NO' && isValueBlank) {
                    const blankInfo = {
                        rowNumber: rowIndex + 1,
                        originalValue: value,
                        valueType: typeof value,
                        isNull: value === null,
                        isUndefined: value === undefined,
                        stringValue: typeof value === 'string' ? `"${value}"` : 'N/A',
                        trimmedValue: typeof value === 'string' ? `"${value.trim()}"` : 'N/A',
                        stringLength: typeof value === 'string' ? value.length : 'N/A',
                        trimmedLength: typeof value === 'string' ? value.trim().length : 'N/A',
                        charCodes: typeof value === 'string' ? Array.from(value).map(c => `${c}(${c.charCodeAt(0)})`).join(' ') : 'N/A',
                        isDash: typeof value === 'string' && value.trim() === '-',
                        isEmpty: typeof value === 'string' && value === '', // Strictly empty, not just whitespace
                        isOnlyWhitespace: typeof value === 'string' && value.trim() === '' && value !== '',
                        isNaN: typeof value === 'number' && isNaN(value),
                        blankReason: (() => {
                            if (value === null) return 'NULL';
                            if (value === undefined) return 'UNDEFINED';
                            if (typeof value === 'string' && value === '') return 'EMPTY_STRING';
                            if (typeof value === 'number' && isNaN(value)) return 'NaN';
                            return 'OTHER';
                        })()
                    };

                    contactNoBlankRows.push(blankInfo);
                    console.log(`🔍 CONTACT_NO Blank Found - Row ${rowIndex + 1}:`, blankInfo);
                }

                return isValueBlank;
            }

            function updateUI() {
                const isPrimary = currentColumnSet === 'primary';
                const expectedColumns = isPrimary ? EXPECTED_COLUMNS_PRIMARY : EXPECTED_COLUMNS_SECONDARY;
                
                // Update buttons
                primaryBtn.className = isPrimary ? 'btn btn-primary' : 'btn btn-secondary';
                secondaryBtn.className = isPrimary ? 'btn btn-secondary' : 'btn btn-primary';
                
                // Update description
                columnDescription.textContent = isPrimary 
                    ? `Business entities: ENTERPRISE_NAME, BUSINESS_REGISTRATION_NUMBER, etc. (${EXPECTED_COLUMNS_PRIMARY.length} columns)`
                    : `Individual customers: FULL_NAME, NATIONAL_IDENTIFIER, OCCUPATION, etc. (${EXPECTED_COLUMNS_SECONDARY.length} columns)`;
                
                // Update debug info
                currentColumnSetSpan.textContent = currentColumnSet;
                expectedCountSpan.textContent = expectedColumns.length;
            }

            function showError(message) {
                errorMessage.textContent = message;
                error.classList.remove('hidden');
                loading.classList.add('hidden');
            }

            function hideError() {
                error.classList.add('hidden');
            }

            function showLoading() {
                loading.classList.remove('hidden');
                hideError();
                results.classList.add('hidden');
            }

            function hideLoading() {
                loading.classList.add('hidden');
            }

            function exportContactNoBlankAnalysis() {
                if (contactNoBlankRows.length === 0) {
                    alert('No CONTACT_NO blank analysis data available. Please upload and analyze a file first.');
                    return;
                }

                // Create CSV content
                const headers = [
                    'Row Number', 'Original Value', 'Value Type', 'String Length', 'Trimmed Length',
                    'Blank Reason', 'Is Null', 'Is Undefined', 'Is Empty String', 'Is Only Whitespace', 'Is Dash', 'Is NaN', 'Character Codes'
                ];

                let csvContent = headers.join(',') + '\n';

                contactNoBlankRows.forEach(row => {
                    const csvRow = [
                        row.rowNumber,
                        `"${row.originalValue}"`,
                        row.valueType,
                        row.stringLength,
                        row.trimmedLength,
                        row.blankReason,
                        row.isNull,
                        row.isUndefined,
                        row.isEmpty,
                        row.isOnlyWhitespace,
                        row.isDash,
                        row.isNaN,
                        `"${row.charCodes}"`
                    ];
                    csvContent += csvRow.join(',') + '\n';
                });

                // Download the file
                const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
                const link = document.createElement('a');
                const url = URL.createObjectURL(blob);
                link.setAttribute('href', url);

                const timestamp = new Date().toISOString().replace(/[:.]/g, '-').slice(0, 19);
                const fileName = currentFile ? currentFile.name.replace(/\.[^/.]+$/, '') : 'data';
                link.setAttribute('download', `${fileName}_CONTACT_NO_Blank_Analysis_${timestamp}.csv`);

                link.style.visibility = 'hidden';
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);

                alert(`Exported ${contactNoBlankRows.length} CONTACT_NO blank records for analysis`);
            }

            function analyzeData(data) {
                // Reset the blank rows tracking
                contactNoBlankRows = [];
                const totalRows = data.length;
                const analysis = {};
                const expectedColumns = currentColumnSet === 'primary' ? EXPECTED_COLUMNS_PRIMARY : EXPECTED_COLUMNS_SECONDARY;
                const ADDRESS_COLUMNS = ['HOME_ADDRESS', 'MAILING_ADDRESS', 'ADD01', 'OFFICE_ADDRESS'];

                console.log('=== ANALYZE DATA DEBUG ===');
                console.log('Current columnSet:', currentColumnSet);
                console.log('Expected columns:', expectedColumns);

                // Initialize analysis
                expectedColumns.forEach(column => {
                    analysis[column] = {
                        blankCount: 0,
                        totalRows: totalRows,
                        blankPercentage: 0,
                        exists: false
                    };
                });

                analysis['ADDRESS_GROUP'] = {
                    blankCount: 0,
                    totalRows: totalRows,
                    blankPercentage: 0,
                    exists: false,
                    isGroup: true
                };

                // Column mapping
                const columnMapping = {};
                if (data.length > 0) {
                    const firstRow = data[0];
                    const fileColumns = Object.keys(firstRow);

                    console.log('File columns found:', fileColumns);

                    fileColumns.forEach((key, index) => {
                        const normalizedKey = key.replace(/\s+/g, '').toUpperCase();
                        console.log(`File column ${index + 1}: "${key}" -> normalized: "${normalizedKey}"`);
                        
                        const matchedColumn = expectedColumns.find(col =>
                            col.replace(/\s+/g, '').toUpperCase() === normalizedKey
                        );
                        
                        if (matchedColumn) {
                            analysis[matchedColumn].exists = true;
                            columnMapping[matchedColumn] = key;
                            console.log(`✓ MATCHED: "${key}" -> ${matchedColumn}`);
                        } else {
                            console.log(`✗ NO MATCH for: "${key}"`);
                        }
                    });

                    // Check address group
                    const addressColumnsExist = ADDRESS_COLUMNS.some(col => analysis[col] && analysis[col].exists);
                    if (addressColumnsExist) {
                        analysis['ADDRESS_GROUP'].exists = true;
                    }
                }

                // Count blanks
                data.forEach((row, rowIndex) => {
                    expectedColumns.forEach(expectedColumn => {
                        if (analysis[expectedColumn].exists) {
                            const actualColumnName = columnMapping[expectedColumn];
                            const value = row[actualColumnName];
                            if (isBlank(value, expectedColumn, rowIndex)) {
                                analysis[expectedColumn].blankCount++;
                                if (expectedColumn === 'NO_OF_PTO' || expectedColumn === 'NO_OF_SIGNATURE') {
                                    console.log(`Row ${rowIndex + 1}: ${expectedColumn} is blank (value: "${value}" = 0 or empty)`);
                                }
                            }
                        }
                    });

                    // Address group blanks
                    if (analysis['ADDRESS_GROUP'].exists) {
                        const allAddressFieldsBlank = ADDRESS_COLUMNS.every(col => {
                            if (analysis[col] && analysis[col].exists) {
                                const actualColumnName = columnMapping[col];
                                return isBlank(row[actualColumnName], col);
                            }
                            return true;
                        });

                        if (allAddressFieldsBlank) {
                            analysis['ADDRESS_GROUP'].blankCount++;
                        }
                    }
                });

                // Calculate percentages
                Object.keys(analysis).forEach(column => {
                    if (analysis[column].exists) {
                        analysis[column].blankPercentage =
                            ((analysis[column].blankCount / totalRows) * 100).toFixed(2);
                    }
                });

                // Summary for CONTACT_NO debugging
                if (analysis['CONTACT_NO'] && analysis['CONTACT_NO'].exists) {
                    console.log(`📊 CONTACT_NO Analysis Summary:`);
                    console.log(`   Total rows: ${totalRows}`);
                    console.log(`   Blank count: ${analysis['CONTACT_NO'].blankCount}`);
                    console.log(`   Blank percentage: ${analysis['CONTACT_NO'].blankPercentage}%`);
                    console.log(`   Expected Excel count: Should be close to ${analysis['CONTACT_NO'].blankCount}`);
                    console.log(`   Check console above for detailed blank value analysis`);
                }

                return analysis;
            }

            function displayResults(analysis) {
                const expectedColumns = currentColumnSet === 'primary' ? EXPECTED_COLUMNS_PRIMARY : EXPECTED_COLUMNS_SECONDARY;
                
                // Clear previous results
                resultsTableBody.innerHTML = '';

                // Add rows for each column
                const allColumns = [...expectedColumns, 'ADDRESS_GROUP'];
                allColumns.forEach(column => {
                    const data = analysis[column];
                    const isAddressGroup = column === 'ADDRESS_GROUP';
                    const displayName = isAddressGroup 
                        ? 'Address Group (HOME_ADDRESS + MAILING_ADDRESS + ADD01 + OFFICE_ADDRESS)' 
                        : column;

                    const row = document.createElement('tr');
                    
                    // Apply row classes
                    if (data.exists) {
                        if (isAddressGroup) {
                            row.className = 'row-address-group';
                        }
                    } else {
                        row.className = 'row-missing';
                    }

                    row.innerHTML = `
                        <td>
                            <div class="font-medium">${displayName}</div>
                            ${isAddressGroup ? '<div class="column-description">Records where ALL 4 address fields are blank</div>' : ''}
                        </td>
                        <td>
                            <span class="status-badge ${data.exists 
                                ? (isAddressGroup ? 'status-group' : 'status-found') 
                                : 'status-missing'}">
                                ${data.exists ? (isAddressGroup ? 'Group' : 'Found') : 'Missing'}
                            </span>
                        </td>
                        <td>${data.exists ? data.blankCount : 'N/A'}</td>
                        <td>${data.totalRows}</td>
                        <td>${data.exists ? `${data.blankPercentage}%` : 'N/A'}</td>
                    `;

                    resultsTableBody.appendChild(row);
                });

                // Update summary
                const foundColumns = Object.entries(analysis).filter(([key, r]) => 
                    key !== 'ADDRESS_GROUP' && r.exists).length;
                const missingColumns = expectedColumns.length - foundColumns;
                const totalRows = Object.values(analysis).find(r => r.totalRows)?.totalRows || 0;

                summaryContent.innerHTML = `
                    <p>• Column set: ${currentColumnSet === 'primary' ? 'Primary (AML Business)' : 'Secondary (Individual)'}</p>
                    <p>• Total expected columns: ${expectedColumns.length}</p>
                    <p>• Columns found in file: ${foundColumns}</p>
                    <p>• Missing columns: ${missingColumns}</p>
                    <p>• Total rows analyzed: ${totalRows}</p>
                    ${analysis['ADDRESS_GROUP']?.exists 
                        ? `<p>• Address group analysis: ${analysis['ADDRESS_GROUP'].blankCount} records with all address fields blank (${analysis['ADDRESS_GROUP'].blankPercentage}%)</p>`
                        : ''}
                    <p class="special-note">• Ultra-strict blank detection: Only truly empty cells (null/undefined or empty strings) count as blank</p>
                    <p class="special-note">• ALL other values are NOT blank: spaces, dashes, zeros, any text or numbers</p>
                    <p class="special-note">• This applies to ALL columns including NO_OF_PTO and NO_OF_SIGNATURE</p>
                `;

                // Show export button if CONTACT_NO analysis was performed
                if (analysis['CONTACT_NO'] && analysis['CONTACT_NO'].exists && contactNoBlankRows.length > 0) {
                    exportBlankAnalysisBtn.style.display = 'inline-block';
                } else {
                    exportBlankAnalysisBtn.style.display = 'none';
                }

                // Show results
                fileName.textContent = currentFile ? currentFile.name : '';
                results.classList.remove('hidden');
                hideLoading();
            }

            async function parseCSV(file) {
                return new Promise((resolve, reject) => {
                    Papa.parse(file, {
                        header: true,
                        skipEmptyLines: true,
                        keepEmptyRows: false,
                        transformHeader: (header) => header.trim(),
                        complete: (results) => {
                            if (results.errors.length > 0) {
                                reject(new Error('CSV parsing error: ' + results.errors[0].message));
                            } else {
                                console.log('CSV parsed successfully');
                                console.log('Raw data sample:', results.data.slice(0, 2));
                                
                                const processedData = results.data.map(row => {
                                    const processedRow = {};
                                    Object.keys(row).forEach(key => {
                                        processedRow[key.trim()] = row[key];
                                    });
                                    return processedRow;
                                });
                                
                                console.log('Processed data columns:', processedData.length > 0 ? Object.keys(processedData[0]) : 'No data');
                                resolve(processedData);
                            }
                        },
                        error: (error) => reject(error)
                    });
                });
            }

            async function parseExcel(file) {
                return new Promise((resolve, reject) => {
                    const reader = new FileReader();
                    reader.onload = (e) => {
                        try {
                            const data = new Uint8Array(e.target.result);
                            const workbook = XLSX.read(data, { type: 'array' });
                            const firstSheetName = workbook.SheetNames[0];
                            const worksheet = workbook.Sheets[firstSheetName];
                            const jsonData = XLSX.utils.sheet_to_json(worksheet);
                            resolve(jsonData);
                        } catch (error) {
                            reject(error);
                        }
                    };
                    reader.onerror = () => reject(new Error('Failed to read file'));
                    reader.readAsArrayBuffer(file);
                });
            }

            async function handleFileUpload(file) {
                showLoading();
                currentFile = file;

                try {
                    let data;
                    const fileExtension = file.name.split('.').pop().toLowerCase();

                    if (fileExtension === 'csv') {
                        data = await parseCSV(file);
                    } else if (['xlsx', 'xls'].includes(fileExtension)) {
                        data = await parseExcel(file);
                    } else {
                        throw new Error('Unsupported file format. Please upload CSV, XLS, or XLSX files.');
                    }

                    if (data.length === 0) {
                        throw new Error('The uploaded file appears to be empty.');
                    }

                    originalData = data;
                    const analysis = analyzeData(data);
                    displayResults(analysis);

                } catch (err) {
                    showError(err.message);
                }
            }

            function handleColumnSetChange(newColumnSet) {
                console.log('=== COLUMN SET CHANGE ===');
                console.log('Changing from:', currentColumnSet, 'to:', newColumnSet);
                
                currentColumnSet = newColumnSet;
                updateUI();
                
                // Re-analyze if we have data
                if (originalData) {
                    console.log('Re-analyzing data with new column set...');
                    const analysis = analyzeData(originalData);
                    displayResults(analysis);
                }
            }

            // Event listeners
            primaryBtn.addEventListener('click', () => handleColumnSetChange('primary'));
            secondaryBtn.addEventListener('click', () => handleColumnSetChange('secondary'));
            exportBlankAnalysisBtn.addEventListener('click', exportContactNoBlankAnalysis);

            dropZone.addEventListener('click', () => fileInput.click());
            fileInput.addEventListener('change', (e) => {
                const files = Array.from(e.target.files);
                if (files.length > 0) {
                    handleFileUpload(files[0]);
                }
            });

            // Drag and drop
            dropZone.addEventListener('dragover', (e) => {
                e.preventDefault();
                dropZone.classList.add('drag-over');
            });

            dropZone.addEventListener('dragleave', (e) => {
                e.preventDefault();
                dropZone.classList.remove('drag-over');
            });

            dropZone.addEventListener('drop', (e) => {
                e.preventDefault();
                dropZone.classList.remove('drag-over');
                const files = Array.from(e.dataTransfer.files);
                if (files.length > 0) {
                    handleFileUpload(files[0]);
                }
            });

            // Initialize
            updateUI();
            
            console.log('AML Analyzer initialized successfully');
        })();
    </script>
</body>
</html>
