<!DOCTYPE html>
<html>
<head>
    <title>AML Column Diagnostic Tool</title>
    <script src="https://unpkg.com/papaparse@5.4.1/papaparse.min.js"></script>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        table { border-collapse: collapse; width: 100%; margin: 10px 0; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
        .found { background-color: #d4edda; color: #155724; }
        .missing { background-color: #f8d7da; color: #721c24; }
        .extra { background-color: #fff3cd; color: #856404; }
    </style>
</head>
<body>
    <h1>AML Column Diagnostic Tool</h1>
    <p>Upload a CSV or Excel file to analyze column matching against the 17 required AML columns.</p>
    
    <input type="file" id="fileInput" accept=".csv,.xlsx,.xls" />
    <div id="results"></div>

    <script>
        const EXPECTED_COLUMNS = [
            'CIF',
            'ENTERPRISE_NAME',
            'BUSINESS_REGISTRATION_NUMBER',
            'COUNTRY_OF_REGISTRATION',
            'DATE_FORM',
            'STATE_OF_REGISTRATION',
            'HOME_ADDRESS',
            'MAILING_ADDRESS',
            'ADD01',
            'DEFAULT',
            'OFFICE_ADDRESS',
            'EMAIL',
            'CONTACT_NO',
            'ANNUALTURNOVER',
            'INDUSTRY_TYPE',
            'CUSTOMER_SEGMENT',
            'UDF_CUSTOMERSEGMENT'
        ];

        function analyzeColumns(fileColumns) {
            const foundColumns = [];
            const missingColumns = [];
            const extraColumns = [];
            const columnMapping = {};

            // Check which expected columns are found
            EXPECTED_COLUMNS.forEach(expectedCol => {
                const matchedCol = fileColumns.find(fileCol => 
                    fileCol.trim().toUpperCase() === expectedCol.toUpperCase()
                );
                
                if (matchedCol) {
                    foundColumns.push({
                        expected: expectedCol,
                        actual: matchedCol,
                        exactMatch: expectedCol === matchedCol
                    });
                    columnMapping[expectedCol] = matchedCol;
                } else {
                    missingColumns.push(expectedCol);
                }
            });

            // Check for extra columns not in expected list
            fileColumns.forEach(fileCol => {
                const isExpected = EXPECTED_COLUMNS.some(expectedCol => 
                    fileCol.trim().toUpperCase() === expectedCol.toUpperCase()
                );
                if (!isExpected) {
                    extraColumns.push(fileCol);
                }
            });

            return {
                foundColumns,
                missingColumns,
                extraColumns,
                columnMapping,
                totalExpected: EXPECTED_COLUMNS.length,
                totalFound: foundColumns.length,
                totalMissing: missingColumns.length,
                totalExtra: extraColumns.length,
                totalInFile: fileColumns.length
            };
        }

        function displayResults(analysis, fileColumns) {
            let output = '<h2>Column Analysis Results</h2>';
            
            // Summary
            output += '<div style="background: #e9ecef; padding: 15px; border-radius: 5px; margin: 10px 0;">';
            output += '<h3>Summary</h3>';
            output += `<p><strong>Total columns in file:</strong> ${analysis.totalInFile}</p>`;
            output += `<p><strong>Expected AML columns:</strong> ${analysis.totalExpected}</p>`;
            output += `<p><strong>Found:</strong> ${analysis.totalFound}</p>`;
            output += `<p><strong>Missing:</strong> ${analysis.totalMissing}</p>`;
            output += `<p><strong>Extra (not required):</strong> ${analysis.totalExtra}</p>`;
            output += '</div>';

            // File columns in order
            output += '<h3>File Columns (in order)</h3>';
            output += '<table>';
            output += '<tr><th>Position</th><th>Column Name</th><th>Status</th></tr>';
            fileColumns.forEach((col, index) => {
                const isExpected = EXPECTED_COLUMNS.some(expectedCol => 
                    col.trim().toUpperCase() === expectedCol.toUpperCase()
                );
                const statusClass = isExpected ? 'found' : 'extra';
                const status = isExpected ? 'Required AML Column' : 'Extra Column';
                output += `<tr class="${statusClass}">`;
                output += `<td>${index + 1}</td>`;
                output += `<td>"${col}"</td>`;
                output += `<td>${status}</td>`;
                output += `</tr>`;
            });
            output += '</table>';

            // Found columns
            if (analysis.foundColumns.length > 0) {
                output += '<h3>Found Columns</h3>';
                output += '<table>';
                output += '<tr><th>Expected Name</th><th>Actual Name in File</th><th>Exact Match</th></tr>';
                analysis.foundColumns.forEach(col => {
                    const exactClass = col.exactMatch ? 'found' : 'extra';
                    output += `<tr class="found">`;
                    output += `<td>${col.expected}</td>`;
                    output += `<td>"${col.actual}"</td>`;
                    output += `<td class="${exactClass}">${col.exactMatch ? 'Yes' : 'No (case/space difference)'}</td>`;
                    output += `</tr>`;
                });
                output += '</table>';
            }

            // Missing columns
            if (analysis.missingColumns.length > 0) {
                output += '<h3>Missing Columns</h3>';
                output += '<table>';
                output += '<tr><th>Missing Column Name</th><th>Suggestions</th></tr>';
                analysis.missingColumns.forEach(col => {
                    // Look for similar column names
                    const suggestions = fileColumns.filter(fileCol => {
                        const similarity = fileCol.toLowerCase().includes(col.toLowerCase()) || 
                                         col.toLowerCase().includes(fileCol.toLowerCase());
                        return similarity;
                    });
                    
                    output += `<tr class="missing">`;
                    output += `<td>${col}</td>`;
                    output += `<td>${suggestions.length > 0 ? 'Similar: ' + suggestions.join(', ') : 'No similar columns found'}</td>`;
                    output += `</tr>`;
                });
                output += '</table>';
            }

            // Extra columns
            if (analysis.extraColumns.length > 0) {
                output += '<h3>Extra Columns (Not Required for AML)</h3>';
                output += '<table>';
                output += '<tr><th>Column Name</th><th>Note</th></tr>';
                analysis.extraColumns.forEach(col => {
                    output += `<tr class="extra">`;
                    output += `<td>"${col}"</td>`;
                    output += `<td>This column is not in the required AML list</td>`;
                    output += `</tr>`;
                });
                output += '</table>';
            }

            return output;
        }

        document.getElementById('fileInput').addEventListener('change', function(e) {
            const file = e.target.files[0];
            if (!file) return;

            const fileExtension = file.name.split('.').pop().toLowerCase();

            if (fileExtension === 'csv') {
                Papa.parse(file, {
                    header: true,
                    skipEmptyLines: true,
                    transformHeader: (header) => header.trim(),
                    complete: (results) => {
                        if (results.data.length > 0) {
                            const fileColumns = Object.keys(results.data[0]);
                            const analysis = analyzeColumns(fileColumns);
                            document.getElementById('results').innerHTML = displayResults(analysis, fileColumns);
                        } else {
                            document.getElementById('results').innerHTML = '<p style="color: red;">No data found in file.</p>';
                        }
                    },
                    error: (error) => {
                        document.getElementById('results').innerHTML = `<p style="color: red;">Error parsing CSV: ${error.message}</p>`;
                    }
                });
            } else {
                document.getElementById('results').innerHTML = '<p style="color: orange;">Excel files not supported in this diagnostic tool. Please use CSV format or the main application.</p>';
            }
        });
    </script>
</body>
</html>
