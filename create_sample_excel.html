<!DOCTYPE html>
<html>
<head>
    <title>Create Sample Excel File</title>
    <script src="https://unpkg.com/xlsx@0.18.5/dist/xlsx.full.min.js"></script>
</head>
<body>
    <h1>Generate Sample Excel File</h1>
    <button onclick="createExcelFile()">Download Sample Excel File</button>

    <script>
        function createExcelFile() {
            const data = [
                ["CIF", "ENTERPRISE_NAME", "BUSINESS_REGISTRATION_NUMBER", "COUNTRY_OF_REGISTRATION", "DATE_FORM", "STATE_OF_REGISTRATION", "HOME_ADDRESS", "MAILING_ADDRESS", "ADD01", "DEFAULT", "OFFICE_ADDRESS", "EMAIL", "CONTACT_NO", "ANNUALTURNOVER", "INDUSTRY_TYPE", "CUSTOMER_SEGMENT", "UDF_CUSTOMERSEGMENT"],
                ["CIF001", "ABC Corporation", "REG123456", "USA", "2023-01-15", "California", "123 Main St", "123 Main St", "Additional Info", "N", "456 Business Ave", "<EMAIL>", "+1-555-0123", 1000000, "Technology", "Corporate", "Premium"],
                ["CIF002", "XYZ Ltd", "", "UK", "2023-02-20", "London", "789 High St", "", "", "", "567 Office Rd", "<EMAIL>", "", 500000, "Finance", "SME", "Standard"],
                ["CIF003", "", "REG789012", "Canada", "", "Ontario", "321 Maple Ave", "321 Maple Ave", "Suite 100", "Y", "321 Maple Ave", "<EMAIL>", "+1-416-555-0199", 2000000, "Manufacturing", "Corporate", "Premium"],
                ["CIF004", "DEF Industries", "REG345678", "Australia", "2023-03-10", "Sydney", "654 George St", "PO Box 123", "Level 5", "N", "654 George St", "", "+61-2-9555-0123", "", "Retail", "SME", ""],
                ["CIF005", "GHI Services", "REG901234", "Singapore", "2023-04-05", "Singapore", "888 Orchard Rd", "888 Orchard Rd", "", "N", "888 Orchard Rd", "<EMAIL>", "+65-6555-0123", 750000, "Services", "Corporate", "Standard"],
                ["", "JKL Company", "REG567890", "Japan", "2023-05-12", "Tokyo", "999 Shibuya", "999 Shibuya", "Floor 10", "Y", "999 Shibuya", "<EMAIL>", "", 300000, "Technology", "", "Premium"],
                ["CIF007", "MNO Corp", "REG234567", "Germany", "2023-06-18", "Berlin", "777 Unter den Linden", "", "Building A", "N", "", "<EMAIL>", "+49-30-555-0123", 1500000, "Automotive", "Corporate", "Standard"],
                ["CIF008", "", "REG678901", "France", "2023-07-22", "Paris", "555 Champs-Élysées", "555 Champs-Élysées", "", "Y", "555 Champs-Élysées", "", "+33-1-55-55-01-23", 800000, "Fashion", "SME", "Premium"],
                ["CIF009", "PQR Holdings", "REG345123", "Italy", "2023-08-30", "Milan", "333 Via Montenapoleone", "PO Box 456", "Suite 200", "N", "333 Via Montenapoleone", "<EMAIL>", "+39-02-555-0123", "", "Real Estate", "Corporate", ""],
                ["CIF010", "STU Enterprises", "", "Spain", "2023-09-15", "Madrid", "111 Gran Vía", "111 Gran Vía", "Office 15", "Y", "111 Gran Vía", "<EMAIL>", "", 600000, "Energy", "SME", "Standard"],
                ["CIF011", "VWX Solutions", "REG789456", "Netherlands", "2023-10-20", "Amsterdam", "222 Damrak", "222 Damrak", "", "N", "222 Damrak", "", "+31-20-555-0123", 900000, "", "Corporate", "Premium"],
                ["CIF012", "", "REG012345", "Belgium", "", "Brussels", "444 Avenue Louise", "PO Box 789", "Floor 8", "Y", "444 Avenue Louise", "<EMAIL>", "+32-2-555-0123", 400000, "Consulting", "SME", ""],
                ["CIF013", "YZA Corporation", "REG456789", "Switzerland", "2023-11-25", "Zurich", "666 Bahnhofstrasse", "666 Bahnhofstrasse", "Suite 300", "N", "", "<EMAIL>", "", 1200000, "Banking", "Corporate", "Premium"],
                ["CIF014", "BCD Ltd", "REG890123", "Austria", "2023-12-01", "Vienna", "888 Ringstrasse", "", "Level 12", "Y", "888 Ringstrasse", "<EMAIL>", "+43-1-555-0123", "", "Insurance", "SME", "Standard"],
                ["CIF015", "", "REG234890", "Sweden", "2024-01-10", "Stockholm", "999 Gamla Stan", "999 Gamla Stan", "Building C", "N", "999 Gamla Stan", "", "+46-8-555-0123", 550000, "Healthcare", "Corporate", ""],
                ["CIF016", "EFG Group", "REG567234", "Norway", "2024-02-14", "Oslo", "777 Karl Johans gate", "PO Box 321", "", "Y", "", "<EMAIL>", "+47-22-555-0123", 850000, "Telecommunications", "SME", "Premium"],
                ["CIF017", "HIJ Industries", "", "Denmark", "2024-03-20", "Copenhagen", "555 Strøget", "555 Strøget", "Office 25", "N", "555 Strøget", "<EMAIL>", "", 700000, "Logistics", "Corporate", "Standard"],
                ["CIF018", "", "REG901567", "Finland", "2024-04-25", "Helsinki", "333 Esplanadi", "333 Esplanadi", "Suite 400", "Y", "333 Esplanadi", "", "+358-9-555-0123", "", "Education", "SME", ""],
                ["CIF019", "KLM Corporation", "REG345901", "Iceland", "2024-05-30", "Reykjavik", "111 Laugavegur", "PO Box 654", "Floor 3", "N", "111 Laugavegur", "<EMAIL>", "+************", 300000, "", "Corporate", "Premium"],
                ["CIF020", "NOP Services", "", "Ireland", "2024-06-15", "Dublin", "222 Grafton Street", "222 Grafton Street", "", "Y", "222 Grafton Street", "<EMAIL>", "", 450000, "Media", "SME", "Standard"]
            ];

            const ws = XLSX.utils.aoa_to_sheet(data);
            const wb = XLSX.utils.book_new();
            XLSX.utils.book_append_sheet(wb, ws, "AML_Data");
            
            XLSX.writeFile(wb, "sample_aml_data.xlsx");
        }
    </script>
</body>
</html>
