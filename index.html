<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AML Data Analyzer - Blank Value Counter</title>
    <script src="https://unpkg.com/react@18/umd/react.production.min.js"></script>
    <script src="https://unpkg.com/react-dom@18/umd/react-dom.production.min.js"></script>
    <script src="https://unpkg.com/@babel/standalone/babel.min.js"></script>
    <script src="https://unpkg.com/xlsx@0.18.5/dist/xlsx.full.min.js"></script>
    <script src="https://unpkg.com/papaparse@5.4.1/papaparse.min.js"></script>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        .drop-zone {
            border: 2px dashed #cbd5e0;
            border-radius: 8px;
            padding: 2rem;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        .drop-zone:hover, .drop-zone.drag-over {
            border-color: #4299e1;
            background-color: #ebf8ff;
        }
        .results-table {
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
        }
    </style>
</head>
<body class="bg-gray-50 min-h-screen">
    <div id="root"></div>

    <script type="text/babel">
        const { useState, useCallback } = React;

        // Define the expected columns for AML analysis (Primary)
        const EXPECTED_COLUMNS_PRIMARY = [
            'CIF',
            'ENTERPRISE_NAME',
            'BUSINESS_REGISTRATION_NUMBER',
            'COUNTRY_OF_REGISTRATION',
            'DATE_FORM',
            'STATE_OF_REGISTRATION',
            'HOME_ADDRESS',
            'MAILING_ADDRESS',
            'ADD01',
            'DEFAULT',
            'OFFICE_ADDRESS',
            'EMAIL',
            'CONTACT_NO',
            'ANNUALTURNOVER',
            'INDUSTRY_TYPE',
            'CUSTOMER_SEGMENT',
            'UDF_CUSTOMERSEGMENT'
        ];

        // Define the secondary columns set
        const EXPECTED_COLUMNS_SECONDARY = [
            'CIF',
            'CONTACT_NO',
            'FULL_NAME',
            'NATIONAL_IDENTIFIER',
            'NO_OF_PTO',
            'OCCUPATION',
            'INDUSTRY_SECTOR',
            'CUSTOMER_SEGMENT',
            'CUSTOMER_SEGMENT_UDF',
            'INCOME_LEVEL',
            'HOME_ADDRESS',
            'MAILING_ADDRESS',
            'ADD01',
            'DEFAULT',
            'OFFICE_ADDRESS',
            'EMPLOYMENTSTATUS',
            'DATE_OF_BIRTH',
            'NATIONALITY',
            'FATHERNAME',
            'NO_OF_SIGNATURE'
        ];

        function AMLDataAnalyzer() {
            const [file, setFile] = useState(null);
            const [results, setResults] = useState(null);
            const [loading, setLoading] = useState(false);
            const [error, setError] = useState(null);
            const [dragOver, setDragOver] = useState(false);
            const [originalData, setOriginalData] = useState(null);
            const [columnSet, setColumnSet] = useState('primary'); // 'primary' or 'secondary'

            const isBlank = (value) => {
                if (value === null || value === undefined) return true;
                if (typeof value === 'string') {
                    const trimmed = value.trim();
                    return trimmed === '' || trimmed === '-';
                }
                if (typeof value === 'number') return isNaN(value);
                return false;
            };

            const analyzeData = (data) => {
                const totalRows = data.length;
                const analysis = {};

                // Get the current column set based on selection
                const EXPECTED_COLUMNS = columnSet === 'primary' ? EXPECTED_COLUMNS_PRIMARY : EXPECTED_COLUMNS_SECONDARY;

                // Address group columns for special grouping logic
                const ADDRESS_COLUMNS = ['HOME_ADDRESS', 'MAILING_ADDRESS', 'ADD01', 'OFFICE_ADDRESS'];

                // Initialize analysis for all expected columns
                EXPECTED_COLUMNS.forEach(column => {
                    analysis[column] = {
                        blankCount: 0,
                        totalRows: totalRows,
                        blankPercentage: 0,
                        exists: false
                    };
                });

                // Add address group analysis
                analysis['ADDRESS_GROUP'] = {
                    blankCount: 0,
                    totalRows: totalRows,
                    blankPercentage: 0,
                    exists: false,
                    isGroup: true
                };

                // Create a mapping of actual column names to expected column names
                const columnMapping = {};
                if (data.length > 0) {
                    const firstRow = data[0];
                    const fileColumns = Object.keys(firstRow);

                    console.log('=== COLUMN MATCHING DEBUG ===');
                    console.log('Column set selected:', columnSet);
                    console.log('File columns found:', fileColumns);
                    console.log('Expected columns:', EXPECTED_COLUMNS);
                    console.log('Expected columns count:', EXPECTED_COLUMNS.length);
                    console.log('File columns count:', fileColumns.length);

                    // Create a detailed comparison
                    console.log('=== DETAILED COLUMN COMPARISON ===');
                    fileColumns.forEach((key, index) => {
                        // More robust normalization - remove all whitespace and convert to uppercase
                        const normalizedKey = key.replace(/\s+/g, '').toUpperCase();
                        console.log(`File column ${index + 1}: "${key}" -> normalized: "${normalizedKey}"`);

                        const matchedColumn = EXPECTED_COLUMNS.find(col =>
                            col.replace(/\s+/g, '').toUpperCase() === normalizedKey
                        );

                        if (matchedColumn) {
                            analysis[matchedColumn].exists = true;
                            columnMapping[matchedColumn] = key;
                            console.log(`✓ MATCHED: "${key}" -> ${matchedColumn}`);
                        } else {
                            console.log(`✗ NO MATCH for: "${key}"`);
                            console.log(`  Normalized: "${normalizedKey}"`);
                            console.log(`  Character codes:`, Array.from(key).map(c => `${c}(${c.charCodeAt(0)})`));

                            // Find closest matches
                            const expectedNormalized = EXPECTED_COLUMNS.map(col => col.replace(/\s+/g, '').toUpperCase());
                            const closeMatches = expectedNormalized.filter(exp =>
                                exp.includes(normalizedKey) || normalizedKey.includes(exp)
                            );
                            if (closeMatches.length > 0) {
                                console.log(`  Possible matches:`, closeMatches);
                            }
                        }
                    });

                    console.log('=== EXPECTED COLUMNS (NORMALIZED) ===');
                    EXPECTED_COLUMNS.forEach((col, index) => {
                        const normalized = col.replace(/\s+/g, '').toUpperCase();
                        console.log(`Expected ${index + 1}: "${col}" -> normalized: "${normalized}"`);
                    });

                    const missingColumns = EXPECTED_COLUMNS.filter(col => !analysis[col].exists);
                    if (missingColumns.length > 0) {
                        console.log('Missing columns:', missingColumns);
                    }

                    // Check if address group should be considered as existing
                    const addressColumnsExist = ADDRESS_COLUMNS.some(col => analysis[col].exists);
                    if (addressColumnsExist) {
                        analysis['ADDRESS_GROUP'].exists = true;
                    }
                }

                // Count blank values for existing columns
                data.forEach((row, rowIndex) => {
                    // Count individual column blanks
                    EXPECTED_COLUMNS.forEach(expectedColumn => {
                        if (analysis[expectedColumn].exists) {
                            const actualColumnName = columnMapping[expectedColumn];
                            const value = row[actualColumnName];
                            if (isBlank(value)) {
                                analysis[expectedColumn].blankCount++;
                                // Debug logging for UDF_CUSTOMERSEGMENT
                                if (expectedColumn === 'UDF_CUSTOMERSEGMENT') {
                                    const reason = value === null || value === undefined ? 'null/undefined' :
                                                 typeof value === 'string' && value.trim() === '' ? 'empty string' :
                                                 typeof value === 'string' && value.trim() === '-' ? 'dash character' :
                                                 'other';
                                    console.log(`Row ${rowIndex + 1}: UDF_CUSTOMERSEGMENT is blank. Value: "${value}" (${reason})`);
                                }
                            }
                        }
                    });

                    // Count address group blanks (all 4 address fields must be blank)
                    if (analysis['ADDRESS_GROUP'].exists) {
                        const allAddressFieldsBlank = ADDRESS_COLUMNS.every(col => {
                            if (analysis[col].exists) {
                                const actualColumnName = columnMapping[col];
                                return isBlank(row[actualColumnName]);
                            }
                            return true; // If column doesn't exist, consider it blank for grouping
                        });

                        if (allAddressFieldsBlank) {
                            analysis['ADDRESS_GROUP'].blankCount++;
                        }
                    }
                });

                // Calculate percentages
                Object.keys(analysis).forEach(column => {
                    if (analysis[column].exists) {
                        analysis[column].blankPercentage =
                            ((analysis[column].blankCount / totalRows) * 100).toFixed(2);
                    }
                });

                // Add row completeness analysis with five options
                // Different exclusions based on column set

                let COMPLETENESS_COLUMNS_OPTION1, COMPLETENESS_COLUMNS_OPTION2, COMPLETENESS_COLUMNS_OPTION3, COMPLETENESS_COLUMNS_OPTION4, COMPLETENESS_COLUMNS_OPTION5;

                if (columnSet === 'primary') {
                    // Primary column set exclusions
                    COMPLETENESS_COLUMNS_OPTION1 = EXPECTED_COLUMNS.filter(col =>
                        !['DEFAULT', 'ADD01', 'CIF', 'DATE_FORM'].includes(col)
                    );
                    COMPLETENESS_COLUMNS_OPTION2 = EXPECTED_COLUMNS.filter(col =>
                        !['DEFAULT', 'ADD01', 'CIF', 'DATE_FORM', 'EMAIL'].includes(col)
                    );
                    COMPLETENESS_COLUMNS_OPTION3 = EXPECTED_COLUMNS.filter(col =>
                        !['CIF', 'DATE_FORM', 'ADD01', 'DEFAULT', 'EMAIL', 'CONTACT_NO'].includes(col)
                    );
                    COMPLETENESS_COLUMNS_OPTION4 = EXPECTED_COLUMNS.filter(col =>
                        !['CIF', 'DATE_FORM', 'ADD01', 'DEFAULT', 'EMAIL', 'CONTACT_NO', 'UDF_CUSTOMERSEGMENT'].includes(col)
                    );
                    COMPLETENESS_COLUMNS_OPTION5 = EXPECTED_COLUMNS.filter(col =>
                        !['CIF', 'DATE_FORM', 'ADD01', 'DEFAULT', 'EMAIL', 'UDF_CUSTOMERSEGMENT'].includes(col)
                    );
                } else {
                    // Secondary column set exclusions
                    COMPLETENESS_COLUMNS_OPTION1 = EXPECTED_COLUMNS.filter(col =>
                        !['DEFAULT', 'ADD01', 'CIF', 'NO_OF_PTO'].includes(col)
                    );
                    COMPLETENESS_COLUMNS_OPTION2 = EXPECTED_COLUMNS.filter(col =>
                        !['DEFAULT', 'ADD01', 'CIF', 'NO_OF_PTO', 'NO_OF_SIGNATURE'].includes(col)
                    );
                    COMPLETENESS_COLUMNS_OPTION3 = EXPECTED_COLUMNS.filter(col =>
                        !['CIF', 'ADD01', 'DEFAULT', 'NO_OF_PTO', 'NO_OF_SIGNATURE', 'EMPLOYMENTSTATUS'].includes(col)
                    );
                    COMPLETENESS_COLUMNS_OPTION4 = EXPECTED_COLUMNS.filter(col =>
                        !['CIF', 'ADD01', 'DEFAULT', 'NO_OF_PTO', 'NO_OF_SIGNATURE', 'EMPLOYMENTSTATUS', 'CUSTOMER_SEGMENT_UDF'].includes(col)
                    );
                    COMPLETENESS_COLUMNS_OPTION5 = EXPECTED_COLUMNS.filter(col =>
                        !['CIF', 'ADD01', 'DEFAULT', 'NO_OF_PTO', 'NO_OF_SIGNATURE'].includes(col)
                    );
                }

                // Address columns for special logic
                const ADDRESS_COMPLETENESS_COLUMNS = ['HOME_ADDRESS', 'MAILING_ADDRESS', 'OFFICE_ADDRESS'];

                // Function to analyze completeness for a given set of columns
                const analyzeCompletenessOption = (columnsToCheck, optionName) => {
                    let completeRows = 0;
                    let incompleteRows = 0;

                    data.forEach((row, rowIndex) => {
                        let rowIsComplete = true;

                        // Check each required column for completeness
                        columnsToCheck.forEach(expectedColumn => {
                            // Special handling for address columns
                            if (ADDRESS_COMPLETENESS_COLUMNS.includes(expectedColumn)) {
                                // Skip individual address column check - will be handled as a group below
                                return;
                            }

                            if (analysis[expectedColumn].exists) {
                                const actualColumnName = columnMapping[expectedColumn];
                                const value = row[actualColumnName];
                                if (isBlank(value)) {
                                    rowIsComplete = false;
                                }
                            } else {
                                // If a required column doesn't exist in the file, row is incomplete
                                rowIsComplete = false;
                            }
                        });

                        // Special address logic: row is complete if ANY of the three address fields has a value
                        // Only check if at least one address column is in the completeness check
                        const addressColumnsInCheck = ADDRESS_COMPLETENESS_COLUMNS.some(col => columnsToCheck.includes(col));
                        if (addressColumnsInCheck) {
                            const addressColumnsExist = ADDRESS_COMPLETENESS_COLUMNS.some(col => analysis[col].exists);
                            if (addressColumnsExist) {
                                const hasAnyAddressValue = ADDRESS_COMPLETENESS_COLUMNS.some(col => {
                                    if (analysis[col].exists) {
                                        const actualColumnName = columnMapping[col];
                                        const value = row[actualColumnName];
                                        return !isBlank(value);
                                    }
                                    return false;
                                });

                                if (!hasAnyAddressValue) {
                                    rowIsComplete = false;
                                }
                            } else {
                                // If none of the address columns exist, row is incomplete
                                rowIsComplete = false;
                            }
                        }

                        if (rowIsComplete) {
                            completeRows++;
                        } else {
                            incompleteRows++;
                        }
                    });

                    return {
                        completeRows,
                        incompleteRows,
                        completePercentage: ((completeRows / totalRows) * 100).toFixed(2),
                        incompletePercentage: ((incompleteRows / totalRows) * 100).toFixed(2),
                        columnsChecked: columnsToCheck.length,
                        excludedColumns: EXPECTED_COLUMNS.filter(col => !columnsToCheck.includes(col))
                    };
                };

                // Analyze all five options
                const option1Results = analyzeCompletenessOption(COMPLETENESS_COLUMNS_OPTION1, 'Option 1');
                const option2Results = analyzeCompletenessOption(COMPLETENESS_COLUMNS_OPTION2, 'Option 2');
                const option3Results = analyzeCompletenessOption(COMPLETENESS_COLUMNS_OPTION3, 'Option 3');
                const option4Results = analyzeCompletenessOption(COMPLETENESS_COLUMNS_OPTION4, 'Option 4');
                const option5Results = analyzeCompletenessOption(COMPLETENESS_COLUMNS_OPTION5, 'Option 5');

                // Add completeness summaries to analysis
                analysis['ROW_COMPLETENESS_OPTION1'] = {
                    totalRows: totalRows,
                    completeRows: option1Results.completeRows,
                    incompleteRows: option1Results.incompleteRows,
                    completePercentage: option1Results.completePercentage,
                    incompletePercentage: option1Results.incompletePercentage,
                    columnsChecked: option1Results.columnsChecked,
                    excludedColumns: option1Results.excludedColumns,
                    isCompleteness: true,
                    optionName: 'Option 1'
                };

                analysis['ROW_COMPLETENESS_OPTION2'] = {
                    totalRows: totalRows,
                    completeRows: option2Results.completeRows,
                    incompleteRows: option2Results.incompleteRows,
                    completePercentage: option2Results.completePercentage,
                    incompletePercentage: option2Results.incompletePercentage,
                    columnsChecked: option2Results.columnsChecked,
                    excludedColumns: option2Results.excludedColumns,
                    isCompleteness: true,
                    optionName: 'Option 2'
                };

                analysis['ROW_COMPLETENESS_OPTION3'] = {
                    totalRows: totalRows,
                    completeRows: option3Results.completeRows,
                    incompleteRows: option3Results.incompleteRows,
                    completePercentage: option3Results.completePercentage,
                    incompletePercentage: option3Results.incompletePercentage,
                    columnsChecked: option3Results.columnsChecked,
                    excludedColumns: option3Results.excludedColumns,
                    isCompleteness: true,
                    optionName: 'Option 3'
                };

                analysis['ROW_COMPLETENESS_OPTION4'] = {
                    totalRows: totalRows,
                    completeRows: option4Results.completeRows,
                    incompleteRows: option4Results.incompleteRows,
                    completePercentage: option4Results.completePercentage,
                    incompletePercentage: option4Results.incompletePercentage,
                    columnsChecked: option4Results.columnsChecked,
                    excludedColumns: option4Results.excludedColumns,
                    isCompleteness: true,
                    optionName: 'Option 4'
                };

                analysis['ROW_COMPLETENESS_OPTION5'] = {
                    totalRows: totalRows,
                    completeRows: option5Results.completeRows,
                    incompleteRows: option5Results.incompleteRows,
                    completePercentage: option5Results.completePercentage,
                    incompletePercentage: option5Results.incompletePercentage,
                    columnsChecked: option5Results.columnsChecked,
                    excludedColumns: option5Results.excludedColumns,
                    isCompleteness: true,
                    optionName: 'Option 5'
                };

                console.log('Row Completeness Analysis - Five Options:');
                console.log('Option 1 - Excludes: DEFAULT, ADD01, CIF, DATE_FORM');
                console.log(`  Complete rows: ${option1Results.completeRows} (${option1Results.completePercentage}%)`);
                console.log(`  Incomplete rows: ${option1Results.incompleteRows} (${option1Results.incompletePercentage}%)`);
                console.log('Option 2 - Excludes: DEFAULT, ADD01, CIF, DATE_FORM, EMAIL');
                console.log(`  Complete rows: ${option2Results.completeRows} (${option2Results.completePercentage}%)`);
                console.log(`  Incomplete rows: ${option2Results.incompleteRows} (${option2Results.incompletePercentage}%)`);
                console.log('Option 3 - Excludes: CIF, DATE_FORM, ADD01, DEFAULT, EMAIL, CONTACT_NO');
                console.log(`  Complete rows: ${option3Results.completeRows} (${option3Results.completePercentage}%)`);
                console.log(`  Incomplete rows: ${option3Results.incompleteRows} (${option3Results.incompletePercentage}%)`);
                console.log('Option 4 - Excludes: CIF, DATE_FORM, ADD01, DEFAULT, EMAIL, CONTACT_NO, UDF_CUSTOMERSEGMENT');
                console.log(`  Complete rows: ${option4Results.completeRows} (${option4Results.completePercentage}%)`);
                console.log(`  Incomplete rows: ${option4Results.incompleteRows} (${option4Results.incompletePercentage}%)`);
                console.log('Option 5 - Excludes: CIF, DATE_FORM, ADD01, DEFAULT, EMAIL, UDF_CUSTOMERSEGMENT');
                console.log(`  Complete rows: ${option5Results.completeRows} (${option5Results.completePercentage}%)`);
                console.log(`  Incomplete rows: ${option5Results.incompleteRows} (${option5Results.incompletePercentage}%)`);

                return analysis;
            };

            const exportOption5CompleteData = () => {
                if (!originalData || !results) {
                    alert('No data available for export');
                    return;
                }

                // Get the current column set based on selection
                const EXPECTED_COLUMNS = columnSet === 'primary' ? EXPECTED_COLUMNS_PRIMARY : EXPECTED_COLUMNS_SECONDARY;

                // Option 5: Different exclusions based on column set
                let COMPLETENESS_COLUMNS_OPTION5;
                if (columnSet === 'primary') {
                    // Primary set: Exclude CIF, DATE_FORM, ADD01, DEFAULT, EMAIL, UDF_CUSTOMERSEGMENT
                    COMPLETENESS_COLUMNS_OPTION5 = EXPECTED_COLUMNS.filter(col =>
                        !['CIF', 'DATE_FORM', 'ADD01', 'DEFAULT', 'EMAIL', 'UDF_CUSTOMERSEGMENT'].includes(col)
                    );
                } else {
                    // Secondary set: Exclude CIF, ADD01, DEFAULT, NO_OF_PTO, NO_OF_SIGNATURE
                    COMPLETENESS_COLUMNS_OPTION5 = EXPECTED_COLUMNS.filter(col =>
                        !['CIF', 'ADD01', 'DEFAULT', 'NO_OF_PTO', 'NO_OF_SIGNATURE'].includes(col)
                    );
                }

                // Address columns for special logic
                const ADDRESS_COMPLETENESS_COLUMNS = ['HOME_ADDRESS', 'MAILING_ADDRESS', 'OFFICE_ADDRESS'];

                // Create a mapping of actual column names to expected column names
                const columnMapping = {};
                if (originalData.length > 0) {
                    const firstRow = originalData[0];
                    const fileColumns = Object.keys(firstRow);

                    fileColumns.forEach(key => {
                        // More robust normalization - remove all whitespace and convert to uppercase
                        const normalizedKey = key.replace(/\s+/g, '').toUpperCase();
                        const matchedColumn = EXPECTED_COLUMNS.find(col =>
                            col.replace(/\s+/g, '').toUpperCase() === normalizedKey
                        );
                        if (matchedColumn) {
                            columnMapping[matchedColumn] = key;
                        }
                    });
                }

                // Filter complete rows based on Option 5 criteria
                const completeRows = originalData.filter((row, rowIndex) => {
                    let rowIsComplete = true;

                    // Check each required column for completeness
                    COMPLETENESS_COLUMNS_OPTION5.forEach(expectedColumn => {
                        // Special handling for address columns
                        if (ADDRESS_COMPLETENESS_COLUMNS.includes(expectedColumn)) {
                            // Skip individual address column check - will be handled as a group below
                            return;
                        }

                        if (results[expectedColumn] && results[expectedColumn].exists) {
                            const actualColumnName = columnMapping[expectedColumn];
                            const value = row[actualColumnName];
                            if (isBlank(value)) {
                                rowIsComplete = false;
                            }
                        } else {
                            // If a required column doesn't exist in the file, row is incomplete
                            rowIsComplete = false;
                        }
                    });

                    // Special address logic: row is complete if ANY of the three address fields has a value
                    const addressColumnsInCheck = ADDRESS_COMPLETENESS_COLUMNS.some(col => COMPLETENESS_COLUMNS_OPTION5.includes(col));
                    if (addressColumnsInCheck) {
                        const addressColumnsExist = ADDRESS_COMPLETENESS_COLUMNS.some(col => results[col] && results[col].exists);
                        if (addressColumnsExist) {
                            const hasAnyAddressValue = ADDRESS_COMPLETENESS_COLUMNS.some(col => {
                                if (results[col] && results[col].exists) {
                                    const actualColumnName = columnMapping[col];
                                    const value = row[actualColumnName];
                                    return !isBlank(value);
                                }
                                return false;
                            });

                            if (!hasAnyAddressValue) {
                                rowIsComplete = false;
                            }
                        } else {
                            // If none of the address columns exist, row is incomplete
                            rowIsComplete = false;
                        }
                    }

                    return rowIsComplete;
                });

                // Convert to CSV format
                if (completeRows.length === 0) {
                    alert('No complete records found for Option 5 criteria');
                    return;
                }

                // Get all column headers from the original data
                const headers = Object.keys(originalData[0]);

                // Create CSV content
                let csvContent = headers.join(',') + '\n';

                completeRows.forEach(row => {
                    const rowValues = headers.map(header => {
                        let value = row[header];
                        // Handle values that contain commas, quotes, or newlines
                        if (value === null || value === undefined) {
                            value = '';
                        } else {
                            value = String(value);
                            // Escape quotes and wrap in quotes if necessary
                            if (value.includes(',') || value.includes('"') || value.includes('\n')) {
                                value = '"' + value.replace(/"/g, '""') + '"';
                            }
                        }
                        return value;
                    });
                    csvContent += rowValues.join(',') + '\n';
                });

                // Create and download the file
                const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
                const link = document.createElement('a');
                const url = URL.createObjectURL(blob);
                link.setAttribute('href', url);

                // Generate filename with timestamp
                const timestamp = new Date().toISOString().replace(/[:.]/g, '-').slice(0, 19);
                const originalFileName = file ? file.name.replace(/\.[^/.]+$/, '') : 'data';
                link.setAttribute('download', `${originalFileName}_Option5_Complete_${timestamp}.csv`);

                link.style.visibility = 'hidden';
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);

                // Show success message
                alert(`Successfully exported ${completeRows.length} complete records to CSV`);
            };

            const exportOption5IncompleteData = () => {
                if (!originalData || !results) {
                    alert('No data available for export');
                    return;
                }

                // Get the current column set based on selection
                const EXPECTED_COLUMNS = columnSet === 'primary' ? EXPECTED_COLUMNS_PRIMARY : EXPECTED_COLUMNS_SECONDARY;

                // Option 5: Different exclusions based on column set
                let COMPLETENESS_COLUMNS_OPTION5;
                if (columnSet === 'primary') {
                    // Primary set: Exclude CIF, DATE_FORM, ADD01, DEFAULT, EMAIL, UDF_CUSTOMERSEGMENT
                    COMPLETENESS_COLUMNS_OPTION5 = EXPECTED_COLUMNS.filter(col =>
                        !['CIF', 'DATE_FORM', 'ADD01', 'DEFAULT', 'EMAIL', 'UDF_CUSTOMERSEGMENT'].includes(col)
                    );
                } else {
                    // Secondary set: Exclude CIF, ADD01, DEFAULT, NO_OF_PTO, NO_OF_SIGNATURE
                    COMPLETENESS_COLUMNS_OPTION5 = EXPECTED_COLUMNS.filter(col =>
                        !['CIF', 'ADD01', 'DEFAULT', 'NO_OF_PTO', 'NO_OF_SIGNATURE'].includes(col)
                    );
                }

                // Address columns for special logic
                const ADDRESS_COMPLETENESS_COLUMNS = ['HOME_ADDRESS', 'MAILING_ADDRESS', 'OFFICE_ADDRESS'];

                // Create a mapping of actual column names to expected column names
                const columnMapping = {};
                if (originalData.length > 0) {
                    const firstRow = originalData[0];
                    const fileColumns = Object.keys(firstRow);

                    fileColumns.forEach(key => {
                        // More robust normalization - remove all whitespace and convert to uppercase
                        const normalizedKey = key.replace(/\s+/g, '').toUpperCase();
                        const matchedColumn = EXPECTED_COLUMNS.find(col =>
                            col.replace(/\s+/g, '').toUpperCase() === normalizedKey
                        );
                        if (matchedColumn) {
                            columnMapping[matchedColumn] = key;
                        }
                    });
                }

                // Filter incomplete rows based on Option 5 criteria
                const incompleteRows = originalData.filter((row, rowIndex) => {
                    let rowIsComplete = true;

                    // Check each required column for completeness
                    COMPLETENESS_COLUMNS_OPTION5.forEach(expectedColumn => {
                        // Special handling for address columns
                        if (ADDRESS_COMPLETENESS_COLUMNS.includes(expectedColumn)) {
                            // Skip individual address column check - will be handled as a group below
                            return;
                        }

                        if (results[expectedColumn] && results[expectedColumn].exists) {
                            const actualColumnName = columnMapping[expectedColumn];
                            const value = row[actualColumnName];
                            if (isBlank(value)) {
                                rowIsComplete = false;
                            }
                        } else {
                            // If a required column doesn't exist in the file, row is incomplete
                            rowIsComplete = false;
                        }
                    });

                    // Special address logic: row is complete if ANY of the three address fields has a value
                    const addressColumnsInCheck = ADDRESS_COMPLETENESS_COLUMNS.some(col => COMPLETENESS_COLUMNS_OPTION5.includes(col));
                    if (addressColumnsInCheck) {
                        const addressColumnsExist = ADDRESS_COMPLETENESS_COLUMNS.some(col => results[col] && results[col].exists);
                        if (addressColumnsExist) {
                            const hasAnyAddressValue = ADDRESS_COMPLETENESS_COLUMNS.some(col => {
                                if (results[col] && results[col].exists) {
                                    const actualColumnName = columnMapping[col];
                                    const value = row[actualColumnName];
                                    return !isBlank(value);
                                }
                                return false;
                            });

                            if (!hasAnyAddressValue) {
                                rowIsComplete = false;
                            }
                        } else {
                            // If none of the address columns exist, row is incomplete
                            rowIsComplete = false;
                        }
                    }

                    // Return incomplete rows (opposite of complete logic)
                    return !rowIsComplete;
                });

                // Convert to CSV format
                if (incompleteRows.length === 0) {
                    alert('No incomplete records found for Option 5 criteria');
                    return;
                }

                // Get all column headers from the original data
                const headers = Object.keys(originalData[0]);

                // Create CSV content
                let csvContent = headers.join(',') + '\n';

                incompleteRows.forEach(row => {
                    const rowValues = headers.map(header => {
                        let value = row[header];
                        // Handle values that contain commas, quotes, or newlines
                        if (value === null || value === undefined) {
                            value = '';
                        } else {
                            value = String(value);
                            // Escape quotes and wrap in quotes if necessary
                            if (value.includes(',') || value.includes('"') || value.includes('\n')) {
                                value = '"' + value.replace(/"/g, '""') + '"';
                            }
                        }
                        return value;
                    });
                    csvContent += rowValues.join(',') + '\n';
                });

                // Create and download the file
                const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
                const link = document.createElement('a');
                const url = URL.createObjectURL(blob);
                link.setAttribute('href', url);

                // Generate filename with timestamp
                const timestamp = new Date().toISOString().replace(/[:.]/g, '-').slice(0, 19);
                const originalFileName = file ? file.name.replace(/\.[^/.]+$/, '') : 'data';
                link.setAttribute('download', `${originalFileName}_Option5_Incomplete_${timestamp}.csv`);

                link.style.visibility = 'hidden';
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);

                // Show success message
                alert(`Successfully exported ${incompleteRows.length} incomplete records to CSV`);
            };

            // Function to handle column set change and re-analyze data
            const handleColumnSetChange = (newColumnSet) => {
                setColumnSet(newColumnSet);

                // Re-analyze data if we have original data
                if (originalData) {
                    // Use setTimeout to ensure state is updated before re-analysis
                    setTimeout(() => {
                        const analysis = analyzeData(originalData);
                        setResults(analysis);
                    }, 0);
                }
            };

            const parseCSV = (file) => {
                return new Promise((resolve, reject) => {
                    Papa.parse(file, {
                        header: true,
                        skipEmptyLines: true,
                        keepEmptyRows: false,
                        transformHeader: (header) => header.trim(),
                        transform: (value, header) => {
                            // Ensure empty strings are preserved for proper blank detection
                            return value;
                        },
                        complete: (results) => {
                            if (results.errors.length > 0) {
                                reject(new Error('CSV parsing error: ' + results.errors[0].message));
                            } else {
                                console.log('=== CSV PARSING DEBUG ===');
                                console.log('Raw CSV results:', results);
                                console.log('Raw data sample:', results.data.slice(0, 2));

                                // Ensure all expected columns exist in each row, even if empty
                                const processedData = results.data.map(row => {
                                    const processedRow = {};
                                    // Copy all existing columns
                                    Object.keys(row).forEach(key => {
                                        processedRow[key.trim()] = row[key];
                                    });
                                    return processedRow;
                                });

                                console.log('Processed data sample:', processedData.slice(0, 2));
                                console.log('Processed data columns:', processedData.length > 0 ? Object.keys(processedData[0]) : 'No data');
                                resolve(processedData);
                            }
                        },
                        error: (error) => reject(error)
                    });
                });
            };

            const parseExcel = (file) => {
                return new Promise((resolve, reject) => {
                    const reader = new FileReader();
                    reader.onload = (e) => {
                        try {
                            const data = new Uint8Array(e.target.result);
                            const workbook = XLSX.read(data, { type: 'array' });
                            const firstSheetName = workbook.SheetNames[0];
                            const worksheet = workbook.Sheets[firstSheetName];
                            const jsonData = XLSX.utils.sheet_to_json(worksheet);
                            resolve(jsonData);
                        } catch (error) {
                            reject(error);
                        }
                    };
                    reader.onerror = () => reject(new Error('Failed to read file'));
                    reader.readAsArrayBuffer(file);
                });
            };

            const handleFileUpload = async (uploadedFile) => {
                setLoading(true);
                setError(null);
                setResults(null);

                try {
                    let data;
                    const fileExtension = uploadedFile.name.split('.').pop().toLowerCase();

                    if (fileExtension === 'csv') {
                        data = await parseCSV(uploadedFile);
                    } else if (['xlsx', 'xls'].includes(fileExtension)) {
                        data = await parseExcel(uploadedFile);
                    } else {
                        throw new Error('Unsupported file format. Please upload CSV, XLS, or XLSX files.');
                    }

                    if (data.length === 0) {
                        throw new Error('The uploaded file appears to be empty.');
                    }

                    const analysis = analyzeData(data);
                    setResults(analysis);
                    setOriginalData(data);
                    setFile(uploadedFile);

                } catch (err) {
                    setError(err.message);
                } finally {
                    setLoading(false);
                }
            };

            const handleDrop = useCallback((e) => {
                e.preventDefault();
                setDragOver(false);
                const files = Array.from(e.dataTransfer.files);
                if (files.length > 0) {
                    handleFileUpload(files[0]);
                }
            }, []);

            const handleDragOver = useCallback((e) => {
                e.preventDefault();
                setDragOver(true);
            }, []);

            const handleDragLeave = useCallback((e) => {
                e.preventDefault();
                setDragOver(false);
            }, []);

            const handleFileSelect = (e) => {
                const files = Array.from(e.target.files);
                if (files.length > 0) {
                    handleFileUpload(files[0]);
                }
            };

            return (
                <div className="container mx-auto px-4 py-8 max-w-6xl">
                    <div className="text-center mb-8">
                        <h1 className="text-3xl font-bold text-gray-800 mb-2">
                            AML Data Analyzer
                        </h1>
                        <p className="text-gray-600">
                            Upload Excel or CSV files to analyze blank values in required columns
                        </p>

                        {/* Column Set Selector */}
                        <div className="mt-4 inline-flex items-center space-x-4 p-3 bg-white rounded-lg border border-gray-200 shadow-sm">
                            <span className="text-sm font-medium text-gray-700">Column Set:</span>
                            <div className="flex space-x-2">
                                <button
                                    onClick={() => handleColumnSetChange('primary')}
                                    className={`px-3 py-1 text-xs font-medium rounded-md transition-colors duration-200 ${
                                        columnSet === 'primary'
                                            ? 'bg-blue-600 text-white'
                                            : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                                    }`}
                                >
                                    Primary (AML Business)
                                </button>
                                <button
                                    onClick={() => handleColumnSetChange('secondary')}
                                    className={`px-3 py-1 text-xs font-medium rounded-md transition-colors duration-200 ${
                                        columnSet === 'secondary'
                                            ? 'bg-blue-600 text-white'
                                            : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                                    }`}
                                >
                                    Secondary (Individual)
                                </button>
                            </div>
                        </div>

                        {/* Column Set Description */}
                        <div className="mt-3 text-xs text-gray-500">
                            {columnSet === 'primary' ? (
                                <span>Business entities: ENTERPRISE_NAME, BUSINESS_REGISTRATION_NUMBER, etc.</span>
                            ) : (
                                <span>Individual customers: FULL_NAME, NATIONAL_IDENTIFIER, OCCUPATION, etc.</span>
                            )}
                        </div>
                    </div>

                    <div className="bg-white rounded-lg shadow-lg p-6 mb-8">
                        <div
                            className={`drop-zone ${dragOver ? 'drag-over' : ''}`}
                            onDrop={handleDrop}
                            onDragOver={handleDragOver}
                            onDragLeave={handleDragLeave}
                            onClick={() => document.getElementById('fileInput').click()}
                        >
                            <div className="text-center">
                                <svg className="mx-auto h-12 w-12 text-gray-400 mb-4" stroke="currentColor" fill="none" viewBox="0 0 48 48">
                                    <path d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
                                </svg>
                                <p className="text-lg text-gray-600 mb-2">
                                    Drop your Excel or CSV file here, or click to browse
                                </p>
                                <p className="text-sm text-gray-500">
                                    Supports .xlsx, .xls, and .csv files
                                </p>
                            </div>
                            <input
                                id="fileInput"
                                type="file"
                                accept=".csv,.xlsx,.xls"
                                onChange={handleFileSelect}
                                className="hidden"
                            />
                        </div>

                        {loading && (
                            <div className="text-center mt-4">
                                <div className="inline-flex items-center">
                                    <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-blue-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                                        <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                    </svg>
                                    Analyzing file...
                                </div>
                            </div>
                        )}

                        {error && (
                            <div className="mt-4 p-4 bg-red-50 border border-red-200 rounded-md">
                                <div className="flex">
                                    <svg className="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                                        <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                                    </svg>
                                    <div className="ml-3">
                                        <h3 className="text-sm font-medium text-red-800">Error</h3>
                                        <p className="text-sm text-red-700 mt-1">{error}</p>
                                    </div>
                                </div>
                            </div>
                        )}
                    </div>

                    {results && (
                        <div className="bg-white rounded-lg shadow-lg p-6">
                            <div className="flex justify-between items-center mb-6">
                                <h2 className="text-2xl font-bold text-gray-800">Analysis Results</h2>
                                {file && (
                                    <div className="text-sm text-gray-600">
                                        File: <span className="font-medium">{file.name}</span>
                                    </div>
                                )}
                            </div>

                            <div className="overflow-x-auto">
                                <table className="min-w-full results-table bg-white border border-gray-200 rounded-lg">
                                    <thead className="bg-gray-50">
                                        <tr>
                                            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider border-b">
                                                Column Name
                                            </th>
                                            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider border-b">
                                                Status
                                            </th>
                                            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider border-b">
                                                Blank Count
                                            </th>
                                            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider border-b">
                                                Total Rows
                                            </th>
                                            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider border-b">
                                                Blank Percentage
                                            </th>
                                        </tr>
                                    </thead>
                                    <tbody className="divide-y divide-gray-200">
                                        {Object.entries(results)
                                            .filter(([column, data]) => !column.startsWith('ROW_COMPLETENESS'))
                                            .map(([column, data]) => {
                                            const isAddressGroup = column === 'ADDRESS_GROUP';
                                            const displayName = isAddressGroup ? 'Address Group (HOME_ADDRESS + MAILING_ADDRESS + ADD01 + OFFICE_ADDRESS)' : column;
                                            const rowClass = data.exists ?
                                                (isAddressGroup ? 'bg-blue-50 hover:bg-blue-100 border-l-4 border-blue-400' : 'hover:bg-gray-50') :
                                                'bg-red-50';

                                            return (
                                                <tr key={column} className={rowClass}>
                                                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                                                        {displayName}
                                                        {isAddressGroup && (
                                                            <div className="text-xs text-gray-500 mt-1">
                                                                Records where ALL 4 address fields are blank
                                                            </div>
                                                        )}
                                                    </td>
                                                    <td className="px-6 py-4 whitespace-nowrap text-sm">
                                                        {data.exists ? (
                                                            <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                                                                isAddressGroup ? 'bg-blue-100 text-blue-800' : 'bg-green-100 text-green-800'
                                                            }`}>
                                                                {isAddressGroup ? 'Group' : 'Found'}
                                                            </span>
                                                        ) : (
                                                            <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                                                Missing
                                                            </span>
                                                        )}
                                                    </td>
                                                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                                        {data.exists ? data.blankCount : 'N/A'}
                                                    </td>
                                                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                                        {data.totalRows}
                                                    </td>
                                                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                                        {data.exists ? `${data.blankPercentage}%` : 'N/A'}
                                                    </td>
                                                </tr>
                                            );
                                        })}
                                    </tbody>
                                </table>
                            </div>

                            {/* Row Completeness Analysis - Five Options */}
                            {(results['ROW_COMPLETENESS_OPTION1'] || results['ROW_COMPLETENESS_OPTION2'] || results['ROW_COMPLETENESS_OPTION3'] || results['ROW_COMPLETENESS_OPTION4'] || results['ROW_COMPLETENESS_OPTION5']) && (
                                <div className="mt-6 p-6 bg-gradient-to-r from-green-50 to-blue-50 border border-green-200 rounded-lg shadow-sm">
                                    <h3 className="text-lg font-bold text-gray-800 mb-4 flex items-center">
                                        <svg className="w-5 h-5 mr-2 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                                            <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                                        </svg>
                                        Row Completeness Analysis - Five Options
                                    </h3>

                                    {/* Option 1 */}
                                    {results['ROW_COMPLETENESS_OPTION1'] && (
                                        <div className="mb-6 p-4 bg-white rounded-lg border border-blue-200">
                                            <h4 className="text-md font-semibold text-blue-800 mb-3">
                                                Option 1: Excludes {columnSet === 'primary' ? 'DEFAULT, ADD01, CIF, DATE_FORM' : 'DEFAULT, ADD01, CIF, NO_OF_PTO'}
                                            </h4>
                                            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-3">
                                                <div className="bg-gray-50 p-3 rounded border">
                                                    <div className="text-xl font-bold text-gray-800">{results['ROW_COMPLETENESS_OPTION1'].totalRows}</div>
                                                    <div className="text-xs text-gray-600">Total Rows</div>
                                                </div>
                                                <div className="bg-green-50 p-3 rounded border border-green-200">
                                                    <div className="text-xl font-bold text-green-600">{results['ROW_COMPLETENESS_OPTION1'].completeRows}</div>
                                                    <div className="text-xs text-gray-600">Complete Rows</div>
                                                    <div className="text-xs text-green-600 font-medium">({results['ROW_COMPLETENESS_OPTION1'].completePercentage}%)</div>
                                                </div>
                                                <div className="bg-red-50 p-3 rounded border border-red-200">
                                                    <div className="text-xl font-bold text-red-600">{results['ROW_COMPLETENESS_OPTION1'].incompleteRows}</div>
                                                    <div className="text-xs text-gray-600">Incomplete Rows</div>
                                                    <div className="text-xs text-red-600 font-medium">({results['ROW_COMPLETENESS_OPTION1'].incompletePercentage}%)</div>
                                                </div>
                                            </div>
                                            <div className="text-xs text-gray-600">
                                                <strong>Excluded columns:</strong> {results['ROW_COMPLETENESS_OPTION1'].excludedColumns.join(', ')}
                                            </div>
                                        </div>
                                    )}

                                    {/* Option 2 */}
                                    {results['ROW_COMPLETENESS_OPTION2'] && (
                                        <div className="mb-6 p-4 bg-white rounded-lg border border-purple-200">
                                            <h4 className="text-md font-semibold text-purple-800 mb-3">
                                                Option 2: Excludes {columnSet === 'primary' ? 'DEFAULT, ADD01, CIF, DATE_FORM, EMAIL' : 'DEFAULT, ADD01, CIF, NO_OF_PTO, NO_OF_SIGNATURE'}
                                            </h4>
                                            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-3">
                                                <div className="bg-gray-50 p-3 rounded border">
                                                    <div className="text-xl font-bold text-gray-800">{results['ROW_COMPLETENESS_OPTION2'].totalRows}</div>
                                                    <div className="text-xs text-gray-600">Total Rows</div>
                                                </div>
                                                <div className="bg-green-50 p-3 rounded border border-green-200">
                                                    <div className="text-xl font-bold text-green-600">{results['ROW_COMPLETENESS_OPTION2'].completeRows}</div>
                                                    <div className="text-xs text-gray-600">Complete Rows</div>
                                                    <div className="text-xs text-green-600 font-medium">({results['ROW_COMPLETENESS_OPTION2'].completePercentage}%)</div>
                                                </div>
                                                <div className="bg-red-50 p-3 rounded border border-red-200">
                                                    <div className="text-xl font-bold text-red-600">{results['ROW_COMPLETENESS_OPTION2'].incompleteRows}</div>
                                                    <div className="text-xs text-gray-600">Incomplete Rows</div>
                                                    <div className="text-xs text-red-600 font-medium">({results['ROW_COMPLETENESS_OPTION2'].incompletePercentage}%)</div>
                                                </div>
                                            </div>
                                            <div className="text-xs text-gray-600">
                                                <strong>Excluded columns:</strong> {results['ROW_COMPLETENESS_OPTION2'].excludedColumns.join(', ')}
                                            </div>
                                        </div>
                                    )}

                                    {/* Option 3 */}
                                    {results['ROW_COMPLETENESS_OPTION3'] && (
                                        <div className="mb-6 p-4 bg-white rounded-lg border border-orange-200">
                                            <h4 className="text-md font-semibold text-orange-800 mb-3">
                                                Option 3: Excludes {columnSet === 'primary' ? 'CIF, DATE_FORM, ADD01, DEFAULT, EMAIL, CONTACT_NO' : 'CIF, ADD01, DEFAULT, NO_OF_PTO, NO_OF_SIGNATURE, EMPLOYMENTSTATUS'}
                                            </h4>
                                            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-3">
                                                <div className="bg-gray-50 p-3 rounded border">
                                                    <div className="text-xl font-bold text-gray-800">{results['ROW_COMPLETENESS_OPTION3'].totalRows}</div>
                                                    <div className="text-xs text-gray-600">Total Rows</div>
                                                </div>
                                                <div className="bg-green-50 p-3 rounded border border-green-200">
                                                    <div className="text-xl font-bold text-green-600">{results['ROW_COMPLETENESS_OPTION3'].completeRows}</div>
                                                    <div className="text-xs text-gray-600">Complete Rows</div>
                                                    <div className="text-xs text-green-600 font-medium">({results['ROW_COMPLETENESS_OPTION3'].completePercentage}%)</div>
                                                </div>
                                                <div className="bg-red-50 p-3 rounded border border-red-200">
                                                    <div className="text-xl font-bold text-red-600">{results['ROW_COMPLETENESS_OPTION3'].incompleteRows}</div>
                                                    <div className="text-xs text-gray-600">Incomplete Rows</div>
                                                    <div className="text-xs text-red-600 font-medium">({results['ROW_COMPLETENESS_OPTION3'].incompletePercentage}%)</div>
                                                </div>
                                            </div>
                                            <div className="text-xs text-gray-600">
                                                <strong>Excluded columns:</strong> {results['ROW_COMPLETENESS_OPTION3'].excludedColumns.join(', ')}
                                            </div>
                                        </div>
                                    )}

                                    {/* Option 4 */}
                                    {results['ROW_COMPLETENESS_OPTION4'] && (
                                        <div className="mb-6 p-4 bg-white rounded-lg border border-green-200">
                                            <h4 className="text-md font-semibold text-green-800 mb-3">
                                                Option 4: Excludes {columnSet === 'primary' ? 'CIF, DATE_FORM, ADD01, DEFAULT, EMAIL, CONTACT_NO, UDF_CUSTOMERSEGMENT' : 'CIF, ADD01, DEFAULT, NO_OF_PTO, NO_OF_SIGNATURE, EMPLOYMENTSTATUS, CUSTOMER_SEGMENT_UDF'}
                                            </h4>
                                            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-3">
                                                <div className="bg-gray-50 p-3 rounded border">
                                                    <div className="text-xl font-bold text-gray-800">{results['ROW_COMPLETENESS_OPTION4'].totalRows}</div>
                                                    <div className="text-xs text-gray-600">Total Rows</div>
                                                </div>
                                                <div className="bg-green-50 p-3 rounded border border-green-200">
                                                    <div className="text-xl font-bold text-green-600">{results['ROW_COMPLETENESS_OPTION4'].completeRows}</div>
                                                    <div className="text-xs text-gray-600">Complete Rows</div>
                                                    <div className="text-xs text-green-600 font-medium">({results['ROW_COMPLETENESS_OPTION4'].completePercentage}%)</div>
                                                </div>
                                                <div className="bg-red-50 p-3 rounded border border-red-200">
                                                    <div className="text-xl font-bold text-red-600">{results['ROW_COMPLETENESS_OPTION4'].incompleteRows}</div>
                                                    <div className="text-xs text-gray-600">Incomplete Rows</div>
                                                    <div className="text-xs text-red-600 font-medium">({results['ROW_COMPLETENESS_OPTION4'].incompletePercentage}%)</div>
                                                </div>
                                            </div>
                                            <div className="text-xs text-gray-600">
                                                <strong>Excluded columns:</strong> {results['ROW_COMPLETENESS_OPTION4'].excludedColumns.join(', ')}
                                            </div>
                                        </div>
                                    )}

                                    {/* Option 5 */}
                                    {results['ROW_COMPLETENESS_OPTION5'] && (
                                        <div className="mb-6 p-4 bg-white rounded-lg border border-indigo-200">
                                            <h4 className="text-md font-semibold text-indigo-800 mb-3">
                                                Option 5: Excludes {columnSet === 'primary' ? 'CIF, DATE_FORM, ADD01, DEFAULT, EMAIL, UDF_CUSTOMERSEGMENT' : 'CIF, ADD01, DEFAULT, NO_OF_PTO, NO_OF_SIGNATURE'}
                                            </h4>
                                            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-3">
                                                <div className="bg-gray-50 p-3 rounded border">
                                                    <div className="text-xl font-bold text-gray-800">{results['ROW_COMPLETENESS_OPTION5'].totalRows}</div>
                                                    <div className="text-xs text-gray-600">Total Rows</div>
                                                </div>
                                                <div className="bg-green-50 p-3 rounded border border-green-200">
                                                    <div className="text-xl font-bold text-green-600">{results['ROW_COMPLETENESS_OPTION5'].completeRows}</div>
                                                    <div className="text-xs text-gray-600">Complete Rows</div>
                                                    <div className="text-xs text-green-600 font-medium">({results['ROW_COMPLETENESS_OPTION5'].completePercentage}%)</div>
                                                </div>
                                                <div className="bg-red-50 p-3 rounded border border-red-200">
                                                    <div className="text-xl font-bold text-red-600">{results['ROW_COMPLETENESS_OPTION5'].incompleteRows}</div>
                                                    <div className="text-xs text-gray-600">Incomplete Rows</div>
                                                    <div className="text-xs text-red-600 font-medium">({results['ROW_COMPLETENESS_OPTION5'].incompletePercentage}%)</div>
                                                </div>
                                            </div>
                                            <div className="text-xs text-gray-600">
                                                <strong>Excluded columns:</strong> {results['ROW_COMPLETENESS_OPTION5'].excludedColumns.join(', ')}
                                            </div>
                                        </div>
                                    )}

                                    <div className="bg-white p-4 rounded-lg border border-gray-200">
                                        <h4 className="font-medium text-gray-800 mb-2">Completeness Criteria (All Options)</h4>
                                        <div className="text-sm text-gray-600">
                                            <p className="mb-2"><strong>Complete Row:</strong> All required columns (after exclusions) contain actual data:</p>
                                            <ul className="list-disc list-inside mb-2 ml-4">
                                                <li>At least ONE of: HOME_ADDRESS, MAILING_ADDRESS, or OFFICE_ADDRESS must have data</li>
                                                <li>No blanks, empty strings, dashes "-", null values, or whitespace-only values</li>
                                            </ul>
                                            <p><strong>Incomplete Row:</strong> Missing data in any required column or all three address fields are blank</p>
                                        </div>
                                    </div>
                                </div>
                            )}

                            {/* Analysis Results 2 - Option 5 Focus */}
                            {results['ROW_COMPLETENESS_OPTION5'] && (
                                <div className="mt-6 p-6 bg-gradient-to-r from-indigo-50 to-purple-50 border border-indigo-200 rounded-lg shadow-sm">
                                    <h3 className="text-lg font-bold text-indigo-800 mb-4 flex items-center">
                                        <svg className="w-5 h-5 mr-2 text-indigo-600" fill="currentColor" viewBox="0 0 20 20">
                                            <path fillRule="evenodd" d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z" clipRule="evenodd" />
                                        </svg>
                                        Analysis Results 2
                                    </h3>

                                    {/* Analysis Results 2 Table - Same structure as Analysis Results 1 */}
                                    <div className="overflow-x-auto mb-6">
                                        <table className="min-w-full results-table bg-white border border-gray-200 rounded-lg">
                                            <thead className="bg-gray-50">
                                                <tr>
                                                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider border-b">
                                                        Column Name
                                                    </th>
                                                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider border-b">
                                                        Status
                                                    </th>
                                                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider border-b">
                                                        Blank Count
                                                    </th>
                                                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider border-b">
                                                        Total Rows
                                                    </th>
                                                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider border-b">
                                                        Blank Percentage
                                                    </th>
                                                </tr>
                                            </thead>
                                            <tbody className="divide-y divide-gray-200">
                                                {Object.entries(results)
                                                    .filter(([column, data]) => !column.startsWith('ROW_COMPLETENESS'))
                                                    .map(([column, data]) => {
                                                    const isAddressGroup = column === 'ADDRESS_GROUP';
                                                    const displayName = isAddressGroup ? 'Address Group (HOME_ADDRESS + MAILING_ADDRESS + ADD01 + OFFICE_ADDRESS)' : column;
                                                    const rowClass = data.exists ?
                                                        (isAddressGroup ? 'bg-blue-50 hover:bg-blue-100 border-l-4 border-blue-400' : 'hover:bg-gray-50') :
                                                        'bg-red-50';

                                                    return (
                                                        <tr key={column} className={rowClass}>
                                                            <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                                                                {displayName}
                                                                {isAddressGroup && (
                                                                    <div className="text-xs text-gray-500 mt-1">
                                                                        Records where ALL 4 address fields are blank
                                                                    </div>
                                                                )}
                                                            </td>
                                                            <td className="px-6 py-4 whitespace-nowrap text-sm">
                                                                {data.exists ? (
                                                                    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                                                                        isAddressGroup ? 'bg-blue-100 text-blue-800' : 'bg-green-100 text-green-800'
                                                                    }`}>
                                                                        {isAddressGroup ? 'Group' : 'Found'}
                                                                    </span>
                                                                ) : (
                                                                    <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                                                        Missing
                                                                    </span>
                                                                )}
                                                            </td>
                                                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                                                {data.exists ? data.blankCount : 'N/A'}
                                                            </td>
                                                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                                                {data.totalRows}
                                                            </td>
                                                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                                                {data.exists ? `${data.blankPercentage}%` : 'N/A'}
                                                            </td>
                                                        </tr>
                                                    );
                                                })}
                                            </tbody>
                                        </table>
                                    </div>

                                    {/* Export Buttons for Option 5 Data */}
                                    <div className="mb-6 p-4 bg-white rounded-lg border border-gray-200">
                                        <h4 className="text-md font-semibold text-gray-800 mb-4">Export Option 5 Records</h4>
                                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                            {/* Complete Records Export */}
                                            <div className="p-4 bg-green-50 rounded-lg border border-green-200">
                                                <div className="flex items-center justify-between">
                                                    <div>
                                                        <h5 className="text-sm font-semibold text-green-800 mb-1">Complete Records</h5>
                                                        <p className="text-xs text-gray-600 mb-2">
                                                            Records that meet all Option 5 criteria
                                                        </p>
                                                        <span className="text-xs text-green-600 font-medium">
                                                            {results['ROW_COMPLETENESS_OPTION5'].completeRows} records available
                                                        </span>
                                                    </div>
                                                    <button
                                                        onClick={exportOption5CompleteData}
                                                        className="inline-flex items-center px-3 py-2 bg-green-600 hover:bg-green-700 text-white text-xs font-medium rounded-md transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2"
                                                        disabled={!originalData || !results || results['ROW_COMPLETENESS_OPTION5'].completeRows === 0}
                                                    >
                                                        <svg className="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                                                        </svg>
                                                        Export CSV
                                                    </button>
                                                </div>
                                            </div>

                                            {/* Incomplete Records Export */}
                                            <div className="p-4 bg-red-50 rounded-lg border border-red-200">
                                                <div className="flex items-center justify-between">
                                                    <div>
                                                        <h5 className="text-sm font-semibold text-red-800 mb-1">Incomplete Records</h5>
                                                        <p className="text-xs text-gray-600 mb-2">
                                                            Records missing required Option 5 data
                                                        </p>
                                                        <span className="text-xs text-red-600 font-medium">
                                                            {results['ROW_COMPLETENESS_OPTION5'].incompleteRows} records available
                                                        </span>
                                                    </div>
                                                    <button
                                                        onClick={exportOption5IncompleteData}
                                                        className="inline-flex items-center px-3 py-2 bg-red-600 hover:bg-red-700 text-white text-xs font-medium rounded-md transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2"
                                                        disabled={!originalData || !results || results['ROW_COMPLETENESS_OPTION5'].incompleteRows === 0}
                                                    >
                                                        <svg className="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                                                        </svg>
                                                        Export CSV
                                                    </button>
                                                </div>
                                            </div>
                                        </div>

                                        <div className="mt-3 p-3 bg-blue-50 rounded border border-blue-200">
                                            <p className="text-xs text-blue-700">
                                                <strong>Note:</strong> Both exports include all original columns from your uploaded file.
                                                Use incomplete records to identify data gaps and improve data quality.
                                            </p>
                                        </div>
                                    </div>

                                    <div className="bg-white p-4 rounded-lg border border-indigo-200 mb-4">
                                        <h4 className="text-md font-semibold text-indigo-800 mb-3">Option 5: Contact-Enabled Business Data Analysis</h4>
                                        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                                            {/* Left Column - Key Metrics */}
                                            <div>
                                                <h5 className="font-medium text-gray-800 mb-3">Key Metrics</h5>
                                                <div className="space-y-3">
                                                    <div className="flex justify-between items-center p-3 bg-indigo-50 rounded">
                                                        <span className="text-sm font-medium text-gray-700">Total Records</span>
                                                        <span className="text-lg font-bold text-indigo-600">{results['ROW_COMPLETENESS_OPTION5'].totalRows}</span>
                                                    </div>
                                                    <div className="flex justify-between items-center p-3 bg-green-50 rounded">
                                                        <span className="text-sm font-medium text-gray-700">Complete Records</span>
                                                        <span className="text-lg font-bold text-green-600">{results['ROW_COMPLETENESS_OPTION5'].completeRows}</span>
                                                    </div>
                                                    <div className="flex justify-between items-center p-3 bg-red-50 rounded">
                                                        <span className="text-sm font-medium text-gray-700">Incomplete Records</span>
                                                        <span className="text-lg font-bold text-red-600">{results['ROW_COMPLETENESS_OPTION5'].incompleteRows}</span>
                                                    </div>
                                                    <div className="flex justify-between items-center p-3 bg-blue-50 rounded">
                                                        <span className="text-sm font-medium text-gray-700">Completion Rate</span>
                                                        <span className="text-lg font-bold text-blue-600">{results['ROW_COMPLETENESS_OPTION5'].completePercentage}%</span>
                                                    </div>
                                                </div>
                                            </div>

                                            {/* Right Column - Analysis Details */}
                                            <div>
                                                <h5 className="font-medium text-gray-800 mb-3">Analysis Details</h5>
                                                <div className="space-y-3">
                                                    <div className="p-3 bg-gray-50 rounded">
                                                        <div className="text-sm font-medium text-gray-700 mb-1">Columns Analyzed</div>
                                                        <div className="text-lg font-bold text-gray-800">{results['ROW_COMPLETENESS_OPTION5'].columnsChecked}</div>
                                                        <div className="text-xs text-gray-500">Required fields + address group</div>
                                                    </div>
                                                    <div className="p-3 bg-yellow-50 rounded">
                                                        <div className="text-sm font-medium text-gray-700 mb-1">Excluded Fields</div>
                                                        <div className="text-xs text-gray-600">{results['ROW_COMPLETENESS_OPTION5'].excludedColumns.join(', ')}</div>
                                                    </div>
                                                    <div className="p-3 bg-purple-50 rounded">
                                                        <div className="text-sm font-medium text-gray-700 mb-1">Key Feature</div>
                                                        <div className="text-xs text-purple-700 font-medium">Includes CONTACT_NO for phone communication</div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    {/* Option 5 Specific Insights */}
                                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                                        <div className="bg-white p-4 rounded-lg border border-gray-200">
                                            <h5 className="font-medium text-gray-800 mb-2">Contact Capability</h5>
                                            <div className="text-sm text-gray-600">
                                                <p className="mb-2">✅ Phone contact enabled via CONTACT_NO</p>
                                                <p className="mb-2">❌ Email excluded for flexibility</p>
                                                <p>🎯 Ideal for phone-based follow-up</p>
                                            </div>
                                        </div>

                                        <div className="bg-white p-4 rounded-lg border border-gray-200">
                                            <h5 className="font-medium text-gray-800 mb-2">Data Optimization</h5>
                                            <div className="text-sm text-gray-600">
                                                <p className="mb-2">📊 Excludes UDF_CUSTOMERSEGMENT</p>
                                                <p className="mb-2">🔧 Maintains core business data</p>
                                                <p>⚖️ Balances utilization vs contact</p>
                                            </div>
                                        </div>

                                        <div className="bg-white p-4 rounded-lg border border-gray-200">
                                            <h5 className="font-medium text-gray-800 mb-2">Use Cases</h5>
                                            <div className="text-sm text-gray-600">
                                                <p className="mb-2">📞 Customer service integration</p>
                                                <p className="mb-2">🔍 Verification processes</p>
                                                <p>📈 Outreach programs</p>
                                            </div>
                                        </div>
                                    </div>

                                    {/* Comparison with Other Options */}
                                    <div className="bg-white p-4 rounded-lg border border-gray-200">
                                        <h5 className="font-medium text-gray-800 mb-3">Option 5 vs Other Options</h5>
                                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                            <div>
                                                <h6 className="text-sm font-medium text-gray-700 mb-2">Advantages over Option 4</h6>
                                                <ul className="text-xs text-gray-600 space-y-1">
                                                    <li>• Enables phone contact capability</li>
                                                    <li>• Supports customer communication</li>
                                                    <li>• Facilitates verification processes</li>
                                                </ul>
                                            </div>
                                            <div>
                                                <h6 className="text-sm font-medium text-gray-700 mb-2">Trade-offs vs Option 4</h6>
                                                <ul className="text-xs text-gray-600 space-y-1">
                                                    <li>• Lower completion rate due to CONTACT_NO requirement</li>
                                                    <li>• May exclude records with missing phone numbers</li>
                                                    <li>• Requires phone data quality management</li>
                                                </ul>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            )}

                            <div className="mt-6 p-4 bg-blue-50 border border-blue-200 rounded-md">
                                <h3 className="text-sm font-medium text-blue-800 mb-2">Column Analysis Summary</h3>
                                <div className="text-sm text-blue-700">
                                    <p>• Column set: {columnSet === 'primary' ? 'Primary (AML Business)' : 'Secondary (Individual)'}</p>
                                    <p>• Total expected columns: {columnSet === 'primary' ? EXPECTED_COLUMNS_PRIMARY.length : EXPECTED_COLUMNS_SECONDARY.length}</p>
                                    <p>• Columns found in file: {Object.entries(results).filter(([key, r]) => key !== 'ADDRESS_GROUP' && !key.startsWith('ROW_COMPLETENESS') && r.exists).length}</p>
                                    <p>• Missing columns: {Object.entries(results).filter(([key, r]) => key !== 'ADDRESS_GROUP' && !key.startsWith('ROW_COMPLETENESS') && !r.exists).length}</p>
                                    <p>• Total rows analyzed: {Object.values(results).find(r => r.totalRows)?.totalRows || 0}</p>
                                    {results['ADDRESS_GROUP']?.exists && (
                                        <p>• Address group analysis: {results['ADDRESS_GROUP'].blankCount} records with all address fields blank ({results['ADDRESS_GROUP'].blankPercentage}%)</p>
                                    )}
                                </div>
                            </div>
                        </div>
                    )}
                </div>
            );
        }

        // Use React 18's createRoot API
        const root = ReactDOM.createRoot(document.getElementById('root'));
        root.render(<AMLDataAnalyzer />);
    </script>
</body>
</html>
