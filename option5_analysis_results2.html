<!DOCTYPE html>
<html>
<head>
    <title>Option 5 Analysis Results 2 - Contact-Enabled Business Data</title>
    <script src="https://unpkg.com/papaparse@5.4.1/papaparse.min.js"></script>
    <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="bg-gray-50 min-h-screen">
    <div class="container mx-auto px-4 py-8 max-w-6xl">
        <div class="text-center mb-8">
            <h1 class="text-3xl font-bold text-indigo-800 mb-2">
                Analysis Results 2 - Option 5 Focus
            </h1>
            <p class="text-gray-600">
                Detailed analysis of Contact-Enabled Business Data completeness
            </p>
        </div>

        <div class="bg-white rounded-lg shadow-lg p-6 mb-8">
            <div class="text-center">
                <input type="file" id="fileInput" accept=".csv,.xlsx,.xls" class="mb-4" />
                <p class="text-sm text-gray-500">Upload your AML data file for Option 5 analysis</p>
            </div>
        </div>

        <div id="results"></div>
    </div>

    <script>
        const EXPECTED_COLUMNS = [
            'CIF', 'ENTERPRISE_NAME', 'BUSINESS_REGISTRATION_NUMBER', 'COUNTRY_OF_REGISTRATION',
            'DATE_FORM', 'STATE_OF_REGISTRATION', 'HOME_ADDRESS', 'MAILING_ADDRESS', 'ADD01',
            'DEFAULT', 'OFFICE_ADDRESS', 'EMAIL', 'CONTACT_NO', 'ANNUALTURNOVER',
            'INDUSTRY_TYPE', 'CUSTOMER_SEGMENT', 'UDF_CUSTOMERSEGMENT'
        ];

        // Option 5: Exclude CIF, DATE_FORM, ADD01, DEFAULT, EMAIL, UDF_CUSTOMERSEGMENT
        const OPTION5_COLUMNS = EXPECTED_COLUMNS.filter(col => 
            !['CIF', 'DATE_FORM', 'ADD01', 'DEFAULT', 'EMAIL', 'UDF_CUSTOMERSEGMENT'].includes(col)
        );

        const ADDRESS_COLUMNS = ['HOME_ADDRESS', 'MAILING_ADDRESS', 'OFFICE_ADDRESS'];

        const isBlank = (value) => {
            if (value === null || value === undefined) return true;
            if (typeof value === 'string') {
                const trimmed = value.trim();
                return trimmed === '' || trimmed === '-';
            }
            if (typeof value === 'number') return isNaN(value);
            return false;
        };

        function analyzeOption5(data) {
            const columnMapping = {};
            const missingColumns = [];
            
            // Map file columns to expected columns
            if (data.length > 0) {
                const fileColumns = Object.keys(data[0]);
                EXPECTED_COLUMNS.forEach(expectedCol => {
                    const matchedCol = fileColumns.find(fileCol => 
                        fileCol.trim().toUpperCase() === expectedCol.toUpperCase()
                    );
                    if (matchedCol) {
                        columnMapping[expectedCol] = matchedCol;
                    } else {
                        missingColumns.push(expectedCol);
                    }
                });
            }

            let completeRows = 0;
            let incompleteRows = 0;
            const rowDetails = [];
            const incompleteReasons = {
                'Missing CONTACT_NO': 0,
                'Missing Address Data': 0,
                'Missing Core Business Data': 0,
                'Multiple Issues': 0
            };

            data.forEach((row, index) => {
                let rowIsComplete = true;
                const issues = [];

                // Check core business columns (excluding address columns)
                OPTION5_COLUMNS.forEach(expectedColumn => {
                    if (ADDRESS_COLUMNS.includes(expectedColumn)) {
                        return; // Skip address columns, handle separately
                    }
                    
                    if (columnMapping[expectedColumn]) {
                        const actualColumnName = columnMapping[expectedColumn];
                        const value = row[actualColumnName];
                        if (isBlank(value)) {
                            rowIsComplete = false;
                            if (expectedColumn === 'CONTACT_NO') {
                                issues.push('Missing CONTACT_NO');
                            } else {
                                issues.push(`Missing ${expectedColumn}`);
                            }
                        }
                    } else {
                        rowIsComplete = false;
                        issues.push(`Column ${expectedColumn} not found`);
                    }
                });

                // Check address requirement (at least one address field must have data)
                const hasAnyAddress = ADDRESS_COLUMNS.some(col => {
                    if (columnMapping[col]) {
                        const actualColumnName = columnMapping[col];
                        const value = row[actualColumnName];
                        return !isBlank(value);
                    }
                    return false;
                });

                if (!hasAnyAddress) {
                    rowIsComplete = false;
                    issues.push('Missing Address Data');
                }

                // Categorize incomplete reasons
                if (!rowIsComplete) {
                    incompleteRows++;
                    if (issues.length > 1) {
                        incompleteReasons['Multiple Issues']++;
                    } else if (issues.includes('Missing CONTACT_NO')) {
                        incompleteReasons['Missing CONTACT_NO']++;
                    } else if (issues.includes('Missing Address Data')) {
                        incompleteReasons['Missing Address Data']++;
                    } else {
                        incompleteReasons['Missing Core Business Data']++;
                    }
                } else {
                    completeRows++;
                }

                rowDetails.push({
                    rowNumber: index + 1,
                    cif: row.CIF || row.cif || `Row ${index + 1}`,
                    isComplete: rowIsComplete,
                    issues: issues
                });
            });

            return {
                totalRows: data.length,
                completeRows,
                incompleteRows,
                completePercentage: ((completeRows / data.length) * 100).toFixed(2),
                incompletePercentage: ((incompleteRows / data.length) * 100).toFixed(2),
                rowDetails,
                incompleteReasons,
                missingColumns,
                requiredColumns: OPTION5_COLUMNS,
                excludedColumns: ['CIF', 'DATE_FORM', 'ADD01', 'DEFAULT', 'EMAIL', 'UDF_CUSTOMERSEGMENT']
            };
        }

        function displayResults(analysis) {
            let html = `
                <div class="bg-gradient-to-r from-indigo-50 to-purple-50 border border-indigo-200 rounded-lg shadow-sm p-6">
                    <h2 class="text-2xl font-bold text-indigo-800 mb-6 flex items-center">
                        <svg class="w-6 h-6 mr-3 text-indigo-600" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z" clip-rule="evenodd" />
                        </svg>
                        Option 5: Contact-Enabled Business Data Analysis
                    </h2>
                    
                    <!-- Key Metrics Grid -->
                    <div class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
                        <div class="bg-white p-4 rounded-lg border border-indigo-200 text-center">
                            <div class="text-2xl font-bold text-indigo-600">${analysis.totalRows}</div>
                            <div class="text-sm text-gray-600">Total Records</div>
                        </div>
                        <div class="bg-white p-4 rounded-lg border border-green-200 text-center">
                            <div class="text-2xl font-bold text-green-600">${analysis.completeRows}</div>
                            <div class="text-sm text-gray-600">Complete Records</div>
                            <div class="text-xs text-green-600 font-medium">(${analysis.completePercentage}%)</div>
                        </div>
                        <div class="bg-white p-4 rounded-lg border border-red-200 text-center">
                            <div class="text-2xl font-bold text-red-600">${analysis.incompleteRows}</div>
                            <div class="text-sm text-gray-600">Incomplete Records</div>
                            <div class="text-xs text-red-600 font-medium">(${analysis.incompletePercentage}%)</div>
                        </div>
                        <div class="bg-white p-4 rounded-lg border border-blue-200 text-center">
                            <div class="text-2xl font-bold text-blue-600">${analysis.requiredColumns.length}</div>
                            <div class="text-sm text-gray-600">Required Fields</div>
                            <div class="text-xs text-blue-600 font-medium">+ Address Group</div>
                        </div>
                    </div>
                    
                    <!-- Incomplete Reasons Breakdown -->
                    <div class="bg-white p-4 rounded-lg border border-gray-200 mb-6">
                        <h3 class="text-lg font-semibold text-gray-800 mb-4">Incomplete Records Breakdown</h3>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            `;

            Object.entries(analysis.incompleteReasons).forEach(([reason, count]) => {
                const percentage = analysis.totalRows > 0 ? ((count / analysis.totalRows) * 100).toFixed(1) : '0.0';
                html += `
                    <div class="flex justify-between items-center p-3 bg-gray-50 rounded">
                        <span class="text-sm font-medium text-gray-700">${reason}</span>
                        <div class="text-right">
                            <span class="text-lg font-bold text-gray-800">${count}</span>
                            <div class="text-xs text-gray-500">${percentage}%</div>
                        </div>
                    </div>
                `;
            });

            html += `
                        </div>
                    </div>
                    
                    <!-- Option 5 Specific Features -->
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
                        <div class="bg-white p-4 rounded-lg border border-purple-200">
                            <h4 class="font-semibold text-purple-800 mb-2">Contact Capability</h4>
                            <ul class="text-sm text-gray-600 space-y-1">
                                <li>✅ CONTACT_NO required</li>
                                <li>📞 Phone-based communication</li>
                                <li>🔍 Verification processes</li>
                            </ul>
                        </div>
                        <div class="bg-white p-4 rounded-lg border border-orange-200">
                            <h4 class="font-semibold text-orange-800 mb-2">Excluded Fields</h4>
                            <div class="text-xs text-gray-600">
                                ${analysis.excludedColumns.join(', ')}
                            </div>
                        </div>
                        <div class="bg-white p-4 rounded-lg border border-green-200">
                            <h4 class="font-semibold text-green-800 mb-2">Use Cases</h4>
                            <ul class="text-sm text-gray-600 space-y-1">
                                <li>📞 Customer service</li>
                                <li>📈 Outreach programs</li>
                                <li>⚖️ Balanced utilization</li>
                            </ul>
                        </div>
                    </div>
                    
                    <!-- Row-by-Row Analysis -->
                    <div class="bg-white p-4 rounded-lg border border-gray-200">
                        <h3 class="text-lg font-semibold text-gray-800 mb-4">Row-by-Row Analysis</h3>
                        <div class="overflow-x-auto">
                            <table class="min-w-full border border-gray-200">
                                <thead class="bg-gray-50">
                                    <tr>
                                        <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">Row</th>
                                        <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">Identifier</th>
                                        <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">Status</th>
                                        <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">Issues</th>
                                    </tr>
                                </thead>
                                <tbody class="divide-y divide-gray-200">
            `;

            analysis.rowDetails.forEach(row => {
                const statusClass = row.isComplete ? 'bg-green-50 text-green-800' : 'bg-red-50 text-red-800';
                const statusText = row.isComplete ? 'Complete' : 'Incomplete';
                const issuesText = row.issues.length > 0 ? row.issues.join(', ') : 'None';
                
                html += `
                    <tr class="${row.isComplete ? '' : 'bg-red-50'}">
                        <td class="px-4 py-2 text-sm text-gray-900">${row.rowNumber}</td>
                        <td class="px-4 py-2 text-sm text-gray-900">${row.cif}</td>
                        <td class="px-4 py-2">
                            <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full ${statusClass}">
                                ${statusText}
                            </span>
                        </td>
                        <td class="px-4 py-2 text-sm text-gray-600">${issuesText}</td>
                    </tr>
                `;
            });

            html += `
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            `;

            return html;
        }

        document.getElementById('fileInput').addEventListener('change', function(e) {
            const file = e.target.files[0];
            if (!file) return;

            Papa.parse(file, {
                header: true,
                skipEmptyLines: true,
                transformHeader: (header) => header.trim(),
                complete: (results) => {
                    if (results.data.length > 0) {
                        const analysis = analyzeOption5(results.data);
                        document.getElementById('results').innerHTML = displayResults(analysis);
                    } else {
                        document.getElementById('results').innerHTML = '<p class="text-red-600">No data found in file.</p>';
                    }
                },
                error: (error) => {
                    document.getElementById('results').innerHTML = `<p class="text-red-600">Error parsing file: ${error.message}</p>`;
                }
            });
        });
    </script>
</body>
</html>
