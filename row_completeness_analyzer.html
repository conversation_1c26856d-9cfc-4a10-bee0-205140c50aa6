<!DOCTYPE html>
<html>
<head>
    <title>AML Row Completeness Analyzer</title>
    <script src="https://unpkg.com/papaparse@5.4.1/papaparse.min.js"></script>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .container { max-width: 800px; margin: 0 auto; }
        .stats-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin: 20px 0; }
        .stat-card { background: #f8f9fa; border: 1px solid #dee2e6; border-radius: 8px; padding: 15px; text-align: center; }
        .stat-number { font-size: 2em; font-weight: bold; margin-bottom: 5px; }
        .complete { color: #28a745; }
        .incomplete { color: #dc3545; }
        .total { color: #007bff; }
        table { width: 100%; border-collapse: collapse; margin: 20px 0; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
        .row-complete { background-color: #d4edda; }
        .row-incomplete { background-color: #f8d7da; }
        .upload-area { border: 2px dashed #ccc; border-radius: 8px; padding: 20px; text-align: center; margin: 20px 0; }
    </style>
</head>
<body>
    <div class="container">
        <h1>AML Row Completeness Analyzer</h1>
        <p>Upload a CSV file to analyze row completeness across all 17 required AML columns.</p>
        
        <div class="upload-area">
            <input type="file" id="fileInput" accept=".csv" />
            <p>Select a CSV file to analyze</p>
        </div>
        
        <div id="results"></div>
    </div>

    <script>
        const EXPECTED_COLUMNS = [
            'CIF', 'ENTERPRISE_NAME', 'BUSINESS_REGISTRATION_NUMBER', 'COUNTRY_OF_REGISTRATION',
            'DATE_FORM', 'STATE_OF_REGISTRATION', 'HOME_ADDRESS', 'MAILING_ADDRESS', 'ADD01',
            'DEFAULT', 'OFFICE_ADDRESS', 'EMAIL', 'CONTACT_NO', 'ANNUALTURNOVER',
            'INDUSTRY_TYPE', 'CUSTOMER_SEGMENT', 'UDF_CUSTOMERSEGMENT'
        ];

        const isBlank = (value) => {
            if (value === null || value === undefined) return true;
            if (typeof value === 'string') {
                const trimmed = value.trim();
                return trimmed === '' || trimmed === '-';
            }
            if (typeof value === 'number') return isNaN(value);
            return false;
        };

        function analyzeRowCompleteness(data) {
            const columnMapping = {};
            const missingColumns = [];

            // Option 1: Exclude DEFAULT, ADD01, CIF, DATE_FORM
            const COMPLETENESS_COLUMNS_OPTION1 = EXPECTED_COLUMNS.filter(col =>
                !['DEFAULT', 'ADD01', 'CIF', 'DATE_FORM'].includes(col)
            );

            // Option 2: Exclude DEFAULT, ADD01, CIF, DATE_FORM, EMAIL
            const COMPLETENESS_COLUMNS_OPTION2 = EXPECTED_COLUMNS.filter(col =>
                !['DEFAULT', 'ADD01', 'CIF', 'DATE_FORM', 'EMAIL'].includes(col)
            );

            // Option 3: Exclude CIF, DATE_FORM, ADD01, DEFAULT, EMAIL, CONTACT_NO
            const COMPLETENESS_COLUMNS_OPTION3 = EXPECTED_COLUMNS.filter(col =>
                !['CIF', 'DATE_FORM', 'ADD01', 'DEFAULT', 'EMAIL', 'CONTACT_NO'].includes(col)
            );

            // Option 4: Exclude CIF, DATE_FORM, ADD01, DEFAULT, EMAIL, CONTACT_NO, UDF_CUSTOMERSEGMENT
            const COMPLETENESS_COLUMNS_OPTION4 = EXPECTED_COLUMNS.filter(col =>
                !['CIF', 'DATE_FORM', 'ADD01', 'DEFAULT', 'EMAIL', 'CONTACT_NO', 'UDF_CUSTOMERSEGMENT'].includes(col)
            );

            // Option 5: Exclude CIF, DATE_FORM, ADD01, DEFAULT, EMAIL, UDF_CUSTOMERSEGMENT
            const COMPLETENESS_COLUMNS_OPTION5 = EXPECTED_COLUMNS.filter(col =>
                !['CIF', 'DATE_FORM', 'ADD01', 'DEFAULT', 'EMAIL', 'UDF_CUSTOMERSEGMENT'].includes(col)
            );

            // Address columns for special logic
            const ADDRESS_COMPLETENESS_COLUMNS = ['HOME_ADDRESS', 'MAILING_ADDRESS', 'OFFICE_ADDRESS'];

            // Map file columns to expected columns
            if (data.length > 0) {
                const fileColumns = Object.keys(data[0]);
                EXPECTED_COLUMNS.forEach(expectedCol => {
                    const matchedCol = fileColumns.find(fileCol =>
                        fileCol.trim().toUpperCase() === expectedCol.toUpperCase()
                    );
                    if (matchedCol) {
                        columnMapping[expectedCol] = matchedCol;
                    } else {
                        missingColumns.push(expectedCol);
                    }
                });
            }

            let completeRows = 0;
            let incompleteRows = 0;
            const rowDetails = [];

            data.forEach((row, index) => {
                let rowIsComplete = true;
                const blankColumns = [];

                // Check each required column for completeness (excluding DEFAULT and ADD01)
                COMPLETENESS_COLUMNS.forEach(expectedColumn => {
                    // Special handling for address columns
                    if (ADDRESS_COMPLETENESS_COLUMNS.includes(expectedColumn)) {
                        // Skip individual address column check - will be handled as a group below
                        return;
                    }

                    if (columnMapping[expectedColumn]) {
                        const actualColumnName = columnMapping[expectedColumn];
                        const value = row[actualColumnName];
                        if (isBlank(value)) {
                            rowIsComplete = false;
                            blankColumns.push(expectedColumn);
                        }
                    } else {
                        rowIsComplete = false;
                        blankColumns.push(expectedColumn + ' (missing column)');
                    }
                });

                // Special address logic: row is complete if ANY of the three address fields has a value
                const addressColumnsExist = ADDRESS_COMPLETENESS_COLUMNS.some(col => columnMapping[col]);
                if (addressColumnsExist) {
                    const hasAnyAddressValue = ADDRESS_COMPLETENESS_COLUMNS.some(col => {
                        if (columnMapping[col]) {
                            const actualColumnName = columnMapping[col];
                            const value = row[actualColumnName];
                            return !isBlank(value);
                        }
                        return false;
                    });

                    if (!hasAnyAddressValue) {
                        rowIsComplete = false;
                        blankColumns.push('All address fields (HOME_ADDRESS, MAILING_ADDRESS, OFFICE_ADDRESS)');
                    }
                } else {
                    // If none of the address columns exist, row is incomplete
                    rowIsComplete = false;
                    blankColumns.push('Address columns (missing)');
                }

                if (rowIsComplete) {
                    completeRows++;
                } else {
                    incompleteRows++;
                }

                rowDetails.push({
                    rowNumber: index + 1,
                    cif: row.CIF || row.cif || 'N/A',
                    isComplete: rowIsComplete,
                    blankColumns: blankColumns
                });
            });

            return {
                totalRows: data.length,
                completeRows,
                incompleteRows,
                completePercentage: ((completeRows / data.length) * 100).toFixed(2),
                incompletePercentage: ((incompleteRows / data.length) * 100).toFixed(2),
                rowDetails,
                missingColumns,
                foundColumns: Object.keys(columnMapping).length,
                expectedColumns: EXPECTED_COLUMNS.length
            };
        }

        function displayResults(analysis) {
            let html = '<h2>Row Completeness Analysis Results</h2>';
            
            // Statistics cards
            html += '<div class="stats-grid">';
            html += `<div class="stat-card">
                        <div class="stat-number total">${analysis.totalRows}</div>
                        <div>Total Rows</div>
                     </div>`;
            html += `<div class="stat-card">
                        <div class="stat-number complete">${analysis.completeRows}</div>
                        <div>Complete Rows</div>
                        <div>(${analysis.completePercentage}%)</div>
                     </div>`;
            html += `<div class="stat-card">
                        <div class="stat-number incomplete">${analysis.incompleteRows}</div>
                        <div>Incomplete Rows</div>
                        <div>(${analysis.incompletePercentage}%)</div>
                     </div>`;
            html += '</div>';

            // Column status
            html += '<h3>Column Status</h3>';
            html += `<p><strong>Found:</strong> ${analysis.foundColumns} out of ${analysis.expectedColumns} required columns</p>`;
            if (analysis.missingColumns.length > 0) {
                html += `<p><strong>Missing columns:</strong> ${analysis.missingColumns.join(', ')}</p>`;
            }

            // Row details table
            html += '<h3>Row-by-Row Analysis</h3>';
            html += '<table>';
            html += '<tr><th>Row #</th><th>CIF</th><th>Status</th><th>Blank Columns</th></tr>';
            
            analysis.rowDetails.forEach(row => {
                const statusClass = row.isComplete ? 'row-complete' : 'row-incomplete';
                const statusText = row.isComplete ? 'Complete' : 'Incomplete';
                const blankText = row.blankColumns.length > 0 ? row.blankColumns.join(', ') : 'None';
                
                html += `<tr class="${statusClass}">`;
                html += `<td>${row.rowNumber}</td>`;
                html += `<td>${row.cif}</td>`;
                html += `<td>${statusText}</td>`;
                html += `<td>${blankText}</td>`;
                html += `</tr>`;
            });
            
            html += '</table>';

            // Summary
            html += '<h3>Summary</h3>';
            html += '<ul>';
            html += `<li><strong>Complete Row:</strong> All required AML columns contain actual data with special rules:</li>`;
            html += `<li style="margin-left: 20px;">• 15 core columns must have data (excludes DEFAULT and ADD01)</li>`;
            html += `<li style="margin-left: 20px;">• At least ONE of: HOME_ADDRESS, MAILING_ADDRESS, or OFFICE_ADDRESS must have data</li>`;
            html += `<li style="margin-left: 20px;">• No blanks, empty strings, dashes "-", null values, or whitespace-only values</li>`;
            html += `<li><strong>Incomplete Row:</strong> Missing data in any required column or all three address fields are blank</li>`;
            html += `<li><strong>Data Quality:</strong> ${analysis.completePercentage}% of rows are complete and ready for AML analysis</li>`;
            html += '</ul>';

            return html;
        }

        document.getElementById('fileInput').addEventListener('change', function(e) {
            const file = e.target.files[0];
            if (!file) return;

            Papa.parse(file, {
                header: true,
                skipEmptyLines: true,
                transformHeader: (header) => header.trim(),
                complete: (results) => {
                    if (results.data.length > 0) {
                        const analysis = analyzeRowCompleteness(results.data);
                        document.getElementById('results').innerHTML = displayResults(analysis);
                    } else {
                        document.getElementById('results').innerHTML = '<p style="color: red;">No data found in file.</p>';
                    }
                },
                error: (error) => {
                    document.getElementById('results').innerHTML = `<p style="color: red;">Error parsing CSV: ${error.message}</p>`;
                }
            });
        });
    </script>
</body>
</html>
