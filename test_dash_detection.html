<!DOCTYPE html>
<html>
<head>
    <title>Test Dash Detection as Blank</title>
</head>
<body>
    <h1>Dash Detection Test</h1>
    <div id="results"></div>

    <script>
        const isBlank = (value) => {
            if (value === null || value === undefined) return true;
            if (typeof value === 'string') {
                const trimmed = value.trim();
                return trimmed === '' || trimmed === '-';
            }
            if (typeof value === 'number') return isNaN(value);
            return false;
        };

        // Test various values
        const testValues = [
            '',           // empty string
            ' ',          // whitespace
            '-',          // dash
            ' - ',        // dash with spaces
            'Premium',    // normal value
            'Standard',   // normal value
            null,         // null
            undefined,    // undefined
            'N/A',        // not considered blank
            '--',         // double dash (not considered blank)
            'SME',        // normal value
        ];

        let output = '<h2>Blank Detection Test Results</h2>';
        output += '<table border="1" style="border-collapse: collapse; margin: 10px 0;">';
        output += '<tr><th>Value</th><th>Type</th><th>Is Blank?</th><th>Explanation</th></tr>';

        testValues.forEach(value => {
            const blank = isBlank(value);
            const displayValue = value === null ? 'null' : 
                                value === undefined ? 'undefined' : 
                                `"${value}"`;
            const explanation = blank ? 
                (value === null || value === undefined) ? 'Null/undefined value' :
                (typeof value === 'string' && value.trim() === '') ? 'Empty or whitespace string' :
                (typeof value === 'string' && value.trim() === '-') ? 'Dash character' :
                'Other blank condition' :
                'Contains actual data';
            
            output += `<tr>`;
            output += `<td>${displayValue}</td>`;
            output += `<td>${typeof value}</td>`;
            output += `<td style="color: ${blank ? 'red' : 'green'}; font-weight: bold;">${blank ? 'YES' : 'NO'}</td>`;
            output += `<td>${explanation}</td>`;
            output += `</tr>`;
        });

        output += '</table>';
        
        output += '<h3>Summary:</h3>';
        output += '<p>Values considered BLANK:</p>';
        output += '<ul>';
        output += '<li>Empty strings ("")</li>';
        output += '<li>Strings with only whitespace (" ")</li>';
        output += '<li><strong>Dash character ("-")</strong></li>';
        output += '<li>Dash with surrounding whitespace (" - ")</li>';
        output += '<li>null values</li>';
        output += '<li>undefined values</li>';
        output += '</ul>';
        
        output += '<p>Values NOT considered blank:</p>';
        output += '<ul>';
        output += '<li>Any string with actual content (Premium, Standard, SME, etc.)</li>';
        output += '<li>Multiple dashes ("--")</li>';
        output += '<li>Other placeholder values like "N/A"</li>';
        output += '</ul>';

        document.getElementById('results').innerHTML = output;
    </script>
</body>
</html>
