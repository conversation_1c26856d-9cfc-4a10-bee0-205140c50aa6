<!DOCTYPE html>
<html>
<head>
    <title>Test UDF_CUSTOMERSEGMENT Blank Detection</title>
    <script src="https://unpkg.com/papaparse@5.4.1/papaparse.min.js"></script>
</head>
<body>
    <h1>UDF_CUSTOMERSEGMENT Blank Detection Test</h1>
    <div id="results"></div>

    <script>
        // Test data with explicit UDF_CUSTOMERSEGMENT blanks
        const testData = `CIF,ENTERPRISE_NAME,UDF_CUSTOMERSEGMENT
CIF001,ABC Corporation,Premium
CIF002,XYZ Ltd,
CIF003,DEF Industries,Standard
CIF004,GHI Services,
CIF005,JKL Company,Premium`;

        const isBlank = (value) => {
            if (value === null || value === undefined) return true;
            if (typeof value === 'string') {
                const trimmed = value.trim();
                return trimmed === '' || trimmed === '-';
            }
            if (typeof value === 'number') return isNaN(value);
            return false;
        };

        Papa.parse(testData, {
            header: true,
            skipEmptyLines: true,
            complete: (results) => {
                const data = results.data;
                console.log('Parsed data:', data);
                
                let output = '<h2>Test Results</h2>';
                output += `<p>Total rows: ${data.length}</p>`;
                output += '<h3>UDF_CUSTOMERSEGMENT Analysis:</h3>';
                
                let blankCount = 0;
                data.forEach((row, index) => {
                    const value = row['UDF_CUSTOMERSEGMENT'];
                    const blank = isBlank(value);
                    output += `<p>Row ${index + 1} (${row.CIF}): "${value}" - ${blank ? 'BLANK' : 'NOT BLANK'}</p>`;
                    if (blank) blankCount++;
                });
                
                output += `<p><strong>Total UDF_CUSTOMERSEGMENT blanks: ${blankCount}</strong></p>`;
                output += '<p>Expected: 2 blanks (CIF002 and CIF004)</p>';
                
                document.getElementById('results').innerHTML = output;
            }
        });

        // Also test with the actual sample data
        fetch('sample_aml_data.csv')
            .then(response => response.text())
            .then(csvText => {
                Papa.parse(csvText, {
                    header: true,
                    skipEmptyLines: true,
                    complete: (results) => {
                        const data = results.data;
                        
                        let output2 = '<h2>Sample Data UDF_CUSTOMERSEGMENT Analysis</h2>';
                        output2 += `<p>Total rows: ${data.length}</p>`;
                        
                        let blankCount = 0;
                        data.forEach((row, index) => {
                            const value = row['UDF_CUSTOMERSEGMENT'];
                            const blank = isBlank(value);
                            if (blank) {
                                output2 += `<p>Row ${index + 1} (${row.CIF}): "${value}" - BLANK</p>`;
                                blankCount++;
                            }
                        });
                        
                        output2 += `<p><strong>Total UDF_CUSTOMERSEGMENT blanks in sample data: ${blankCount}</strong></p>`;
                        
                        document.getElementById('results').innerHTML += output2;
                    }
                });
            })
            .catch(err => {
                document.getElementById('results').innerHTML += '<p>Could not load sample_aml_data.csv for testing</p>';
            });
    </script>
</body>
</html>
