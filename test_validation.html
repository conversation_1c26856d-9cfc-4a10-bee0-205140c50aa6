<!DOCTYPE html>
<html>
<head>
    <title>Test Validation for AML Analyzer</title>
    <script src="https://unpkg.com/papaparse@5.4.1/papaparse.min.js"></script>
</head>
<body>
    <h1>Test Validation Results</h1>
    <div id="results"></div>

    <script>
        // Test data that matches our test_address_grouping.csv
        const testData = `CIF,ENTERPRISE_NAME,BUSINESS_REGISTRATION_NUMBER,COUNTRY_OF_REGISTRATION,DATE_FORM,STATE_OF_REGISTRATION,HOME_ADDRESS,MAILING_ADDRESS,ADD01,OFFICE_ADDRESS,EMAIL,CONTACT_NO,ANNUALTURNOVER,INDUSTRY_TYPE,CUSTOMER_SEGMENT,UDF_CUSTOMERSEGMENT,DEFAULT
CIF001,ABC Corporation,REG123456,USA,2023-01-15,California,123 Main St,123 Main St,Additional Info,456 Business Ave,<EMAIL>,+1-555-0123,1000000,Technology,Corporate,Premium,N
CIF002,XYZ Ltd,REG789012,UK,2023-02-20,London,,,,,<EMAIL>,,500000,Finance,SME,Standard,N
CIF003,DEF Industries,REG345678,Canada,,Ontario,321 Maple Ave,,Suite 100,321 Maple Ave,<EMAIL>,+1-416-555-0199,2000000,Manufacturing,Corporate,Premium,Y
CIF004,GHI Services,REG901234,Australia,2023-03-10,Sydney,,,,,<EMAIL>,+65-6555-0123,750000,Services,Corporate,Standard,N
CIF005,,REG567890,Singapore,2023-04-05,Singapore,888 Orchard Rd,888 Orchard Rd,,888 Orchard Rd,<EMAIL>,,300000,Technology,,Premium,Y`;

        const EXPECTED_COLUMNS = [
            'CIF', 'ENTERPRISE_NAME', 'BUSINESS_REGISTRATION_NUMBER', 'COUNTRY_OF_REGISTRATION',
            'DATE_FORM', 'STATE_OF_REGISTRATION', 'HOME_ADDRESS', 'MAILING_ADDRESS', 'ADD01',
            'DEFAULT', 'OFFICE_ADDRESS', 'EMAIL', 'CONTACT_NO', 'ANNUALTURNOVER',
            'INDUSTRY_TYPE', 'CUSTOMER_SEGMENT', 'UDF_CUSTOMERSEGMENT'
        ];

        const ADDRESS_COLUMNS = ['HOME_ADDRESS', 'MAILING_ADDRESS', 'ADD01', 'OFFICE_ADDRESS'];

        const isBlank = (value) => {
            if (value === null || value === undefined) return true;
            if (typeof value === 'string') return value.trim() === '';
            if (typeof value === 'number') return isNaN(value);
            return false;
        };

        Papa.parse(testData, {
            header: true,
            skipEmptyLines: true,
            complete: (results) => {
                const data = results.data;
                console.log('Parsed data:', data);
                
                let output = '<h2>Test Results</h2>';
                output += `<p>Total rows: ${data.length}</p>`;
                
                // Test individual column blank counts
                output += '<h3>Individual Column Analysis:</h3>';
                EXPECTED_COLUMNS.forEach(column => {
                    let blankCount = 0;
                    data.forEach(row => {
                        if (isBlank(row[column])) {
                            blankCount++;
                        }
                    });
                    output += `<p>${column}: ${blankCount} blanks</p>`;
                });
                
                // Test address group logic
                output += '<h3>Address Group Analysis:</h3>';
                let addressGroupBlanks = 0;
                data.forEach((row, index) => {
                    const allAddressBlank = ADDRESS_COLUMNS.every(col => isBlank(row[col]));
                    if (allAddressBlank) {
                        addressGroupBlanks++;
                        output += `<p>Row ${index + 1}: All address fields blank</p>`;
                    }
                });
                output += `<p><strong>Total records with all address fields blank: ${addressGroupBlanks}</strong></p>`;
                
                // Expected results for validation
                output += '<h3>Expected Results:</h3>';
                output += '<p>Based on test data:</p>';
                output += '<ul>';
                output += '<li>CIF002: All 4 address fields are blank</li>';
                output += '<li>CIF004: All 4 address fields are blank</li>';
                output += '<li>Expected address group blank count: 2</li>';
                output += '</ul>';
                
                document.getElementById('results').innerHTML = output;
            }
        });
    </script>
</body>
</html>
