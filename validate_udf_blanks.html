<!DOCTYPE html>
<html>
<head>
    <title>Validate UDF_CUSTOMERSEGMENT Blanks</title>
    <script src="https://unpkg.com/papaparse@5.4.1/papaparse.min.js"></script>
</head>
<body>
    <h1>UDF_CUSTOMERSEGMENT Blank Validation</h1>
    <input type="file" id="fileInput" accept=".csv" />
    <div id="results"></div>

    <script>
        const isBlank = (value) => {
            if (value === null || value === undefined) return true;
            if (typeof value === 'string') {
                const trimmed = value.trim();
                return trimmed === '' || trimmed === '-';
            }
            if (typeof value === 'number') return isNaN(value);
            return false;
        };

        document.getElementById('fileInput').addEventListener('change', function(e) {
            const file = e.target.files[0];
            if (!file) return;

            Papa.parse(file, {
                header: true,
                skipEmptyLines: true,
                keepEmptyRows: false,
                transformHeader: (header) => header.trim(),
                complete: (results) => {
                    const data = results.data;
                    let output = '<h2>Analysis Results</h2>';
                    output += `<p>Total rows: ${data.length}</p>`;
                    output += '<h3>UDF_CUSTOMERSEGMENT Column Analysis:</h3>';
                    
                    let blankCount = 0;
                    let hasColumn = false;
                    
                    // Check if column exists
                    if (data.length > 0) {
                        hasColumn = 'UDF_CUSTOMERSEGMENT' in data[0];
                        output += `<p>Column exists: ${hasColumn}</p>`;
                    }
                    
                    if (hasColumn) {
                        output += '<table border="1" style="border-collapse: collapse; margin: 10px 0;">';
                        output += '<tr><th>Row</th><th>CIF</th><th>UDF_CUSTOMERSEGMENT Value</th><th>Is Blank?</th></tr>';
                        
                        data.forEach((row, index) => {
                            const value = row['UDF_CUSTOMERSEGMENT'];
                            const blank = isBlank(value);
                            if (blank) blankCount++;
                            
                            output += `<tr>`;
                            output += `<td>${index + 1}</td>`;
                            output += `<td>${row.CIF || 'N/A'}</td>`;
                            output += `<td>"${value}"</td>`;
                            output += `<td style="color: ${blank ? 'red' : 'green'};">${blank ? 'YES' : 'NO'}</td>`;
                            output += `</tr>`;
                        });
                        
                        output += '</table>';
                        output += `<h3>Summary:</h3>`;
                        output += `<p><strong>Total UDF_CUSTOMERSEGMENT blanks: ${blankCount}</strong></p>`;
                        output += `<p><strong>Percentage: ${((blankCount / data.length) * 100).toFixed(2)}%</strong></p>`;
                    } else {
                        output += '<p style="color: red;">UDF_CUSTOMERSEGMENT column not found in the file!</p>';
                    }
                    
                    document.getElementById('results').innerHTML = output;
                },
                error: (error) => {
                    document.getElementById('results').innerHTML = `<p style="color: red;">Error parsing file: ${error.message}</p>`;
                }
            });
        });
    </script>
</body>
</html>
